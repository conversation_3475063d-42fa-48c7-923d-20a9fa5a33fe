"use client";

import { useEffect, useState } from "react";
import { But<PERSON>, buttonVariants } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuSub,
	DropdownMenuSubContent,
	DropdownMenuSubTrigger,
	DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Sheet, Sheet<PERSON>ontent, SheetHeader, SheetTitle } from "@/components/ui/sheet";
import {
	SparklesIcon,
	Square,
	RectangleHorizontal,
	RectangleVertical,
	ImagePlus,
	X,
	CoinsIcon,
	CheckIcon,
	PaletteIcon,
	FileImageIcon,
	LoaderIcon,
	LayersIcon,
	Download,
	ChevronLeftIcon,
} from "lucide-react";
import { useSession } from "@/lib/auth-client";
import { useUserStore } from "@/store/useUserStore";
import { useSignInBoxOpenStore } from "@/store/useSignInBoxOpenStore";
import { usePlanBoxOpenStore } from "@/store/usePlanBoxOpenStore";
import { toast } from "sonner";
import { ofetch } from "ofetch";
import { AuthError, Credits402Error, handleError, IgnoreError } from "@/@types/error";
import { SubmitButton } from "@/components/ui/custom/submit-button";
import { downloadImageFromBase64, downloadImageFromBase64WithWatermark, fileToBase64, imageUrlToBase64 } from "@/lib/file/utils-file";
import { cn } from "@/lib/utils";
import { useSubmitTimer } from "@/hooks/use-submit-timer";
import { useMobile } from "@/hooks/use-mobile";
import { uploadFile } from "@/lib/file/upload-file";
import { getInitialImageModel, ImageModel, imageWithSeriesModels } from "@/config/image-models-config";
import { Badge } from "../ui/badge";
import { IMAGE_SIZE_LIMIT_ } from "@/lib/constants";
import { useDropzone } from "react-dropzone";
import { useSearchParams } from "next/navigation";
import { Icons } from "../icon/icons";
import { sendGTMEvent } from "@next/third-parties/google";
import { EVENT_GEN_IMAGE_TO_IMAGE, EVENT_GEN_TEXT_TO_IMAGE } from "@/lib/track-events";
import { decodeBase64Url } from "@/lib/utils-url";
import { Hint } from "../ui/custom/hint";

type ImageSizeType = {
	ratio: string;
	width: number;
	height: number;
	label: string;
	icon: any;
};
const IMAGE_SIZES: ImageSizeType[] = [
	{ ratio: "1:1", width: 1024, height: 1024, label: "Square", icon: Square },
	{ ratio: "2:3", width: 683, height: 1024, label: "Portrait", icon: RectangleVertical },
	{ ratio: "3:4", width: 768, height: 1024, label: "Traditional", icon: RectangleVertical },
	// { ratio: "4:5", width: 819, height: 1024, label: "Social Post", icon: RectangleVertical },
	{ ratio: "9:16", width: 576, height: 1024, label: "Social Story", icon: RectangleVertical },
	{ ratio: "3:2", width: 1024, height: 683, label: "Standard", icon: RectangleHorizontal },
	{ ratio: "4:3", width: 1024, height: 768, label: "Classic", icon: RectangleHorizontal },
	{ ratio: "16:9", width: 1024, height: 576, label: "Widescreen", icon: RectangleHorizontal },
];
const isImageModelAspectRatioExist = (model: ImageModel, imageSize: ImageSizeType) => {
	if (model.id.startsWith("imagen")) return ["1:1", "3:4", "4:3", "9:16", "16:9"].includes(imageSize.ratio);
	return true;
};
const isImageModelNumImagesExist = (model: ImageModel, numImages: number) => {
	if (model.id.startsWith("recraft")) return numImages === 1;
	return true;
};

// Function to parse base64 and extract prompt and model
const parseBase64Params = (base64Str: string) => {
	try {
		const payload = JSON.parse(decodeBase64Url(base64Str));

		return {
			prompt: (payload.prompt as string) || "",
			model: (payload.model as string) || "",
		};
	} catch (error) {
		console.error("Failed to decode base64:", error);
		return { prompt: "", model: "" };
	}
};

export default function ImageGenClient() {
	const searchParams = useSearchParams();
	const { data: session } = useSession();
	const { user, refreshUser, hasPaid: userHasPaid, isLoaded: userIsLoaded } = useUserStore();
	const { setSignInBoxOpen } = useSignInBoxOpenStore();
	const { setPlanBoxOpen } = usePlanBoxOpenStore();
	const isMobile = useMobile();

	const modelParam = searchParams.get("model");
	const initialModel = getInitialImageModel(modelParam);

	const [visibility, setVisibility] = useState<string>("public");
	const [model, setModel] = useState<ImageModel>(initialModel);
	const [modelStyle, setModelStyle] = useState<string | null>(initialModel.modelStyle ? initialModel.modelStyle[0].id : null);
	const [prompt, setPrompt] = useState("");
	const [imageSize, setImageSize] = useState<ImageSizeType>(IMAGE_SIZES[0]);
	const [numImages, setNumImages] = useState<number>(1);
	const [originImage, setOriginImage] = useState<{
		url: string;
		base64: string;
	} | null>(null);

	// Mobile model selector states
	const [showMobileModelSelector, setShowMobileModelSelector] = useState(false);
	const [mobileModelView, setMobileModelView] = useState<"series" | "models">("series");
	const [selectedSeries, setSelectedSeries] = useState<(typeof imageWithSeriesModels)[0] | null>(null);

	useEffect(() => {
		const paramsBase64Str = searchParams.get("params");
		if (paramsBase64Str) {
			const { prompt: paramPrompt, model: paramModel } = parseBase64Params(paramsBase64Str);

			// Set prompt if available
			if (paramPrompt) {
				setPrompt(paramPrompt);
			}

			// Set model if available and valid
			if (paramModel) {
				// Find the model in the available models
				const foundModel = imageWithSeriesModels.flatMap((series) => series.models).find((modelOption) => modelOption.id === paramModel);

				if (foundModel) {
					setModel(foundModel);
					// Set model style if the model has styles
					if (foundModel.modelStyle) {
						setModelStyle(foundModel.modelStyle[0].id);
					}
					// Check if current image size is compatible with the new model
					if (!isImageModelAspectRatioExist(foundModel, imageSize)) {
						setImageSize(IMAGE_SIZES[0]);
					}
					// Check if current number of images is compatible with the new model
					if (!isImageModelNumImagesExist(foundModel, numImages)) {
						setNumImages(1);
					}
				}
			}
		}
	}, []);

	const [uploadingImage, setUploadingImage] = useState<boolean>(false);
	const {
		getRootProps,
		getInputProps,
		open: openDropzone,
	} = useDropzone({
		noClick: true,
		multiple: false,
		accept: {
			"image/jpeg": [],
			"image/png": [],
			"image/webp": [],
		},
		maxFiles: 1,
		onError: console.error,
		onDrop: async (acceptedFiles, fileRejections, event) => {
			if (!session) {
				setSignInBoxOpen(true);
				return;
			}
			if (fileRejections.length > 0) {
				const message = fileRejections.at(0)?.errors.at(0)?.message;
				console.error?.(new Error(message));
				return;
			}
			let file = acceptedFiles[0];
			if (!file) return;

			if (file.size > IMAGE_SIZE_LIMIT_) {
				toast.warning("Image exceeds 4MB. Please upload a smaller one.");
				return;
			}

			try {
				setUploadingImage(true);
				const { file_url } = await uploadFile(file);
				// const file_url = "";
				const base64 = await fileToBase64(file);
				setOriginImage({
					url: file_url,
					base64,
				});
			} catch (error: any) {
				console.error("Failed to upload image:", error.message);
				if (error instanceof AuthError) {
					setSignInBoxOpen(true);
					return;
				}
				toast.error(`Upload image failed: ${error.message}`);
			} finally {
				setUploadingImage(false);
			}
		},
	});

	const [previewImageBase64s, setPreviewImageBase64s] = useState<string[] | null>(null);
	// https://static.dreampik.art/dev/media/202508/202508150198ad190a727182b675aa69ce8fd3e0.jpeg
	const [submitting, setSubmitting] = useState(false);
	const { seconds } = useSubmitTimer(submitting);
	const handleGenerateImage = async () => {
		if (submitting) return;
		if (!session) {
			setSignInBoxOpen(true);
			return;
		}
		const promtpTrim = prompt.trim();
		if (!promtpTrim) return;

		if (!model.textToImage && !originImage) {
			toast.warning("This model should have an image input.");
			return;
		}

		sendGTMEvent({
			event: originImage?.url ? EVENT_GEN_IMAGE_TO_IMAGE : EVENT_GEN_TEXT_TO_IMAGE,
			membership_level: user?.membershipLevel,
			model: model.id,
		});

		try {
			setPreviewImageBase64s(Array(numImages).fill(null));
			setSubmitting(true);
			const { status, message, resultUrls } = await ofetch("/api/v1/image/generate-image", {
				method: "POST",
				body: {
					model: model.id,
					prompt: promtpTrim,
					size: {
						ratio: imageSize.ratio,
						width: imageSize.width,
						height: imageSize.height,
					},
					numImages: numImages,
					image: originImage?.url,
					modelStyle: modelStyle,
					visibility: visibility,
				},
			});
			handleError(status, message);
			refreshUser();
			// const resultUrls = [
			// 	"https://d32s1zkpjdc4b1.cloudfront.net/output/18220959-c7e1-4f70-9a6c-3479a6fe4881-u2_b13edcbc-9723-46c0-95cb-99cadec85930.jpeg",
			// 	"https://v3.fal.media/files/rabbit/voFEj_podAvkaiEcyiHoB_image.png",
			// ];
			if (resultUrls && resultUrls.length > 0) {
				// Create a copy of the current array to modify
				const tempBase64s = Array(resultUrls.length).fill(null);
				// Process each URL and update the state as each completes
				for (let i = 0; i < resultUrls.length; i++) {
					const base64 = await imageUrlToBase64(resultUrls[i] as string);
					tempBase64s[i] = base64;
					setPreviewImageBase64s([...tempBase64s]);
				}
			}

			toast.success("Generate success.");
		} catch (error: any) {
			console.error("Failed to generate image:", error.message);
			if (error instanceof AuthError) {
				setSignInBoxOpen(true);
				return;
			}

			if (error instanceof IgnoreError) {
				return;
			}
			if (error instanceof Credits402Error) {
				toast.warning("You do not have enough credits.", {
					action: {
						label: "Get more",
						onClick: () => setPlanBoxOpen(true),
					},
				});
				return;
			}
			toast.error(error.message || "Generate failed.");
		} finally {
			setSubmitting(false);
		}
	};

	const [downloading, setDownloading] = useState(false);

	// Mobile model selector handlers
	const handleMobileModelSelect = (selectedModel: ImageModel) => {
		if (!userHasPaid && selectedModel.pro) {
			setPlanBoxOpen(true);
			return;
		}
		setModel(selectedModel);

		if (!isImageModelAspectRatioExist(selectedModel, imageSize)) {
			setImageSize(IMAGE_SIZES[0]);
		}
		if (!isImageModelNumImagesExist(selectedModel, numImages)) {
			setNumImages(1);
		}
		if (selectedModel.modelStyle) {
			setModelStyle(selectedModel.modelStyle[0].id);
		}
		setShowMobileModelSelector(false);
		setMobileModelView("series");
		setSelectedSeries(null);
	};

	const handleMobileSeriesSelect = (series: (typeof imageWithSeriesModels)[0]) => {
		setSelectedSeries(series);
		setMobileModelView("models");
	};

	const handleMobileBackToSeries = () => {
		setMobileModelView("series");
		setSelectedSeries(null);
	};

	return (
		<div className="flex w-full flex-col items-center gap-6">
			<div className="bg-muted w-full rounded-xl px-4 py-2 backdrop-blur-3xl md:rounded-3xl">
				{originImage && model.imageToImage && (
					<div className="group relative h-14 w-24">
						<img
							src={originImage.base64}
							alt="Model"
							className="h-full w-full rounded-md object-cover"
							onContextMenu={(e) => e.preventDefault()}
							onDragStart={(e) => e.preventDefault()}
						/>
						<button
							className="absolute -top-1.5 -right-1.5 z-10 rounded-full"
							onClick={(e) => {
								e.stopPropagation();
								setOriginImage(null);
							}}
						>
							<X className="size-[18px] rounded-full bg-zinc-900 p-1 text-white" strokeWidth={3} />
						</button>
					</div>
				)}
				<Textarea
					placeholder="Describe an image and click generate..."
					value={prompt}
					maxLength={1500}
					onChange={(e) => setPrompt(e.target.value)}
					className="min-h-[88px] resize-none border-none px-0 shadow-none placeholder:text-base focus-visible:text-base focus-visible:ring-0 md:text-base [&::-webkit-scrollbar]:hidden"
				/>

				<div className="mt-2 mb-1 flex items-center justify-between gap-1">
					<div className="flex flex-wrap items-center gap-1">
						<Hint label={`${visibility === "public" ? "Public: Anyone can see and use" : "Private: Only you can see and use"}`}>
							<div
								className={cn(
									buttonVariants({ variant: "secondary", size: "sm" }),
									"hover:ring-input h-8 cursor-pointer gap-0.5 rounded-full bg-white py-0 text-xs shadow-none hover:bg-white hover:ring-2",
								)}
								onClick={() => {
									if (visibility === "public" && !userHasPaid) {
										setPlanBoxOpen(true);
										return;
									}
									if (visibility === "public") {
										setVisibility("private");
									} else {
										setVisibility("public");
									}
								}}
							>
								{visibility === "public" ? "Public" : "Private"}
								{!userHasPaid && <Icons.Lock className="size-3.5" />}
							</div>
						</Hint>

						{/* Model Selector - Desktop */}
						{!isMobile && (
							<DropdownMenu>
								<DropdownMenuTrigger asChild>
									<Button
										variant="secondary"
										size="sm"
										className="hover:ring-input justify-between gap-0.5 rounded-full bg-white text-xs font-medium shadow-none hover:bg-white hover:ring-2"
									>
										<img className="size-[14px]" src={model.logo} alt="" />
										{model?.name}
									</Button>
								</DropdownMenuTrigger>
								<DropdownMenuContent
									className={cn(
										// "[&::-webkit-scrollbar-thumb]:bg-input [&::-webkit-scrollbar-thumb]:show [&::-webkit-scrollbar]:my-2 [&::-webkit-scrollbar]:w-1 [&::-webkit-scrollbar-thumb]:rounded-full",
										"w-auto rounded-xl",
									)}
								>
									{imageWithSeriesModels.map((series, index) => (
										<DropdownMenuSub key={index}>
											<DropdownMenuSubTrigger
												className={cn(
													"my-1 cursor-pointer items-center gap-3 rounded-lg p-3",
													model.name.includes(series.name) && "bg-muted",
												)}
											>
												<div className="flex flex-col gap-1">
													<div className="flex items-center gap-2">
														<img className="size-4" src={series.logo} alt="" />
														<p className="font-semibold">{series.name}</p>
														{series.new && <Badge className="bg-action rounded-sm px-1.5 py-[1px] text-[10px]">New</Badge>}
													</div>
													<p className="text-muted-foreground">{series.description}</p>
												</div>
											</DropdownMenuSubTrigger>
											<DropdownMenuSubContent className={cn("min-w-[300px] rounded-xl px-0 py-1")}>
												<div
													className={cn(
														"max-h-[500px] space-y-1 overflow-y-auto px-1",
														"[&::-webkit-scrollbar-thumb]:bg-input [&::-webkit-scrollbar-thumb]:show [&::-webkit-scrollbar]:my-2 [&::-webkit-scrollbar]:w-1 [&::-webkit-scrollbar-thumb]:rounded-full",
													)}
												>
													{series.models.map((modelOption, index) => (
														<DropdownMenuItem
															key={index}
															className={cn(
																"w-full cursor-pointer items-center rounded-lg p-3",
																model.id === modelOption.id && "bg-muted",
																!userHasPaid && modelOption.pro && "opacity-60",
															)}
															onClick={() => {
																if (!userHasPaid && modelOption.pro) {
																	setPlanBoxOpen(true);
																	return;
																}
																setModel(modelOption);

																if (!isImageModelAspectRatioExist(modelOption, imageSize)) {
																	setImageSize(IMAGE_SIZES[0]);
																}
																if (!isImageModelNumImagesExist(modelOption, numImages)) {
																	setNumImages(1);
																}
																setModel(modelOption);
																if (modelOption.modelStyle) {
																	setModelStyle(modelOption.modelStyle[0].id);
																}
															}}
														>
															<div className="flex w-full flex-row items-center justify-between gap-3">
																<div className="flex flex-col gap-0.5">
																	<p className="font-medium">
																		{modelOption.name}
																		{modelOption.new && (
																			<Badge className="bg-action ml-1 rounded-sm px-1.5 py-[1px] text-[10px]">New</Badge>
																		)}
																	</p>
																	<p className="text-muted-foreground text-xs">{modelOption.description}</p>
																	<div className="flex flex-row flex-wrap items-center gap-1">
																		{modelOption.imageInputType && (
																			<Badge
																				variant="secondary"
																				className="bg-input/60 rounded-sm px-1.5 py-[1px] text-[10px]"
																			>
																				<FileImageIcon className="size-2.5" />
																				{modelOption.imageInputType}
																			</Badge>
																		)}
																		{modelOption.modelStyle && (
																			<Badge
																				variant="secondary"
																				className="bg-input/60 rounded-sm px-1.5 py-[1px] text-[10px]"
																			>
																				<PaletteIcon className="size-2.5" />
																				Model style
																			</Badge>
																		)}
																		<Badge
																			variant="secondary"
																			className="bg-input/60 rounded-sm px-1.5 py-[1px] text-[10px]"
																		>
																			<CoinsIcon className="size-2.5" />
																			{modelOption.credits} credits
																		</Badge>
																	</div>
																</div>
																{model.id === modelOption.id && (
																	<p className="flex h-4 w-4 shrink-0 items-center justify-center rounded-full bg-black">
																		<CheckIcon className="size-3 text-white" strokeWidth={3} />
																	</p>
																)}
															</div>
														</DropdownMenuItem>
													))}
												</div>
											</DropdownMenuSubContent>
										</DropdownMenuSub>
									))}
								</DropdownMenuContent>
							</DropdownMenu>
						)}

						{/* Model Selector - Mobile */}
						{isMobile && (
							<>
								<Button
									variant="secondary"
									size="sm"
									className="hover:ring-input justify-between gap-0.5 rounded-full bg-white text-xs font-medium shadow-none hover:bg-white hover:ring-2"
									onClick={() => setShowMobileModelSelector(true)}
								>
									<img className="size-[14px]" src={model.logo} alt="" />
									{model?.name}
								</Button>

								<Sheet open={showMobileModelSelector} onOpenChange={setShowMobileModelSelector}>
									<SheetContent side="bottom" className="h-[80vh] gap-0 rounded-t-xl">
										<SheetHeader className={cn(mobileModelView === "series" ? "mb-4 h-0 p-0" : "")}>
											<SheetTitle className="flex items-center gap-2">
												{mobileModelView === "models" && (
													<Button
														variant="ghost"
														size="sm"
														onClick={handleMobileBackToSeries}
														className="text-muted-foreground h-auto p-1 font-semibold"
													>
														<ChevronLeftIcon className="size-4" />
														Go back
													</Button>
												)}
											</SheetTitle>
										</SheetHeader>

										<div className="overflow-y-auto px-4 pb-4">
											{mobileModelView === "series" && (
												<>
													{imageWithSeriesModels.map((series, index) => (
														<div
															key={index}
															className={cn(
																"hover:bg-muted cursor-pointer rounded-lg p-4",
																model.name.includes(series.name) && "bg-muted",
															)}
															onClick={() => handleMobileSeriesSelect(series)}
														>
															<div className="flex items-center gap-3">
																<img className="size-6" src={series.logo} alt="" />
																<div className="flex-1">
																	<div className="flex items-center gap-2">
																		<p className="font-semibold">{series.name}</p>
																		{series.new && (
																			<Badge className="bg-action rounded-sm px-1.5 py-[1px] text-[10px]">New</Badge>
																		)}
																	</div>
																	<p className="text-muted-foreground text-sm">{series.description}</p>
																</div>
															</div>
														</div>
													))}
												</>
											)}

											{mobileModelView === "models" && selectedSeries && (
												<>
													{selectedSeries.models.map((modelOption, index) => (
														<div
															key={index}
															className={cn(
																"cursor-pointer rounded-lg p-4",
																model.id === modelOption.id ? "bg-muted" : "hover:bg-muted",
																!userHasPaid && modelOption.pro && "opacity-60",
															)}
															onClick={() => handleMobileModelSelect(modelOption)}
														>
															<div className="flex items-center justify-between gap-3">
																<div className="flex-1">
																	<div className="flex items-center gap-2">
																		<p className="font-medium">{modelOption.name}</p>
																		{modelOption.new && (
																			<Badge className="bg-action rounded-sm px-1.5 py-[1px] text-[10px]">New</Badge>
																		)}
																	</div>
																	<p className="text-muted-foreground text-sm">{modelOption.description}</p>
																	<div className="mt-2 flex flex-row flex-wrap items-center gap-1">
																		{modelOption.imageInputType && (
																			<Badge
																				variant="secondary"
																				className="bg-input/60 rounded-sm px-1.5 py-[1px] text-[10px]"
																			>
																				<FileImageIcon className="size-2.5" />
																				{modelOption.imageInputType}
																			</Badge>
																		)}
																		{modelOption.modelStyle && (
																			<Badge
																				variant="secondary"
																				className="bg-input/60 rounded-sm px-1.5 py-[1px] text-[10px]"
																			>
																				<PaletteIcon className="size-2.5" />
																				Model style
																			</Badge>
																		)}
																		<Badge
																			variant="secondary"
																			className="bg-input/60 rounded-sm px-1.5 py-[1px] text-[10px]"
																		>
																			<CoinsIcon className="size-2.5" />
																			{modelOption.credits} credits
																		</Badge>
																	</div>
																</div>
																{model.id === modelOption.id && (
																	<div className="flex h-6 w-6 shrink-0 items-center justify-center rounded-full bg-black">
																		<CheckIcon className="size-4 text-white" strokeWidth={3} />
																	</div>
																)}
															</div>
														</div>
													))}
												</>
											)}
										</div>
									</SheetContent>
								</Sheet>
							</>
						)}

						{model.imageToImage && (
							<div>
								<div
									{...getRootProps()}
									className={cn(
										buttonVariants({ variant: "secondary", size: "sm" }),
										"hover:ring-input h-8 cursor-pointer gap-0.5 rounded-full bg-white py-0 text-xs shadow-none hover:bg-white hover:ring-2",
										(originImage || uploadingImage) && "cursor-not-allowed opacity-60",
									)}
									onClick={() => {
										if (originImage || uploadingImage) return;
										openDropzone();
									}}
								>
									{uploadingImage ? <LoaderIcon className="text-foreground size-3.5 animate-spin" /> : <ImagePlus className="size-3.5" />}
									Image
									<input {...getInputProps()} />
								</div>
							</div>
						)}

						{!(model.noAspectRatio === "all" || (model.noAspectRatio === "image-to-image" && originImage)) && (
							<DropdownMenu>
								<DropdownMenuTrigger asChild>
									<Button
										variant="secondary"
										size="sm"
										className="hover:ring-input justify-between rounded-full bg-white text-xs font-medium shadow-none hover:bg-white hover:ring-2"
									>
										<div className="flex items-center gap-0.5">
											<imageSize.icon className="size-3.5" />
											{imageSize.ratio}
										</div>
									</Button>
								</DropdownMenuTrigger>
								<DropdownMenuContent className="w-[200px]">
									<p className="text-muted-foreground px-2 py-1.5 text-sm">Aspect ratio</p>
									{IMAGE_SIZES.map((sizeOption, index) => {
										if (!isImageModelAspectRatioExist(model, sizeOption)) return null;
										return (
											<DropdownMenuItem
												key={index}
												className={cn(
													"group w-full cursor-pointer justify-between",
													imageSize.ratio === sizeOption.ratio && "bg-muted",
												)}
												onClick={() => setImageSize(sizeOption)}
											>
												<div className="flex items-center gap-2">
													<sizeOption.icon className="size-4" />
													{sizeOption.ratio}
													<span className="text-muted-foreground group-hover:text-secondary-foreground text-[12px]">
														{sizeOption.label}
													</span>
												</div>
												{imageSize.ratio === sizeOption.ratio && (
													<p className="flex h-4 w-4 items-center justify-center rounded-full bg-black">
														<CheckIcon className="size-3 text-white" strokeWidth={3} />
													</p>
												)}
											</DropdownMenuItem>
										);
									})}
								</DropdownMenuContent>
							</DropdownMenu>
						)}

						<DropdownMenu>
							<DropdownMenuTrigger asChild>
								<Button
									variant="secondary"
									size="sm"
									className="hover:ring-input justify-between rounded-full bg-white text-xs font-medium shadow-none hover:bg-white hover:ring-2"
								>
									<div className="flex items-center gap-0.5">
										<LayersIcon className="size-3.5" />
										{numImages}
									</div>
								</Button>
							</DropdownMenuTrigger>
							<DropdownMenuContent className="w-[160px]">
								<p className="text-muted-foreground px-2 py-1.5 text-sm">Number of images</p>
								{[1, 2, 3, 4].map((numImage, index) => {
									if (!isImageModelNumImagesExist(model, numImage)) return null;
									return (
										<DropdownMenuItem
											key={index}
											className={cn(
												"w-full cursor-pointer justify-between",
												numImages === numImage && "bg-muted",
												!userHasPaid && numImage > 2 && "opacity-60",
											)}
											onClick={() => {
												if (!userHasPaid && numImage > 2) {
													setPlanBoxOpen(true);
													return;
												}
												setNumImages(numImage);
											}}
										>
											<div className="flex items-center gap-2">{numImage}</div>
											{numImages === numImage && (
												<p className="flex h-4 w-4 items-center justify-center rounded-full bg-black">
													<CheckIcon className="size-3 text-white" strokeWidth={3} />
												</p>
											)}
										</DropdownMenuItem>
									);
								})}
							</DropdownMenuContent>
						</DropdownMenu>

						{model.modelStyle && (
							<DropdownMenu>
								<DropdownMenuTrigger asChild>
									<Button
										variant="secondary"
										size="sm"
										className="hover:ring-input justify-between rounded-full bg-white text-xs font-medium shadow-none hover:bg-white hover:ring-2"
									>
										<div className="flex items-center gap-0.5">
											<PaletteIcon className="size-3.5" />
											{model.modelStyle.find((modelStyleOption) => modelStyleOption.id === modelStyle)?.name}
										</div>
									</Button>
								</DropdownMenuTrigger>
								<DropdownMenuContent className="w-[140px]">
									<p className="text-muted-foreground px-2 py-1.5 text-sm">Model Style</p>
									{model.modelStyle.map((modelStyleOption, index) => (
										<DropdownMenuItem
											key={index}
											className={cn("w-full cursor-pointer justify-between", modelStyle === modelStyleOption.id && "bg-muted")}
											onClick={() => setModelStyle(modelStyleOption.id)}
										>
											<div className="flex items-center gap-2">{modelStyleOption.name}</div>
											{modelStyle === modelStyleOption.id && (
												<p className="flex h-4 w-4 items-center justify-center rounded-full bg-black">
													<CheckIcon className="size-3 text-white" strokeWidth={3} />
												</p>
											)}
										</DropdownMenuItem>
									))}
								</DropdownMenuContent>
							</DropdownMenu>
						)}
					</div>

					<SubmitButton
						isSubmitting={submitting}
						onClick={handleGenerateImage}
						disabled={submitting || prompt.trim().length === 0}
						className="bg-foreground hover:bg-foreground h-10"
					>
						<SparklesIcon className="h-4 w-4" />
						<span className="md:block">Generate</span>
						{session && (
							<p className="text-secondary flex flex-row items-center gap-0.5 px-2 py-0.5 text-[11px]">
								(<CoinsIcon className="size-3" />
								{model.credits * numImages})
							</p>
						)}
					</SubmitButton>
				</div>
			</div>

			{previewImageBase64s && (
				<div
					className={cn(
						"grid w-full gap-4",
						"grid-cols-2 md:grid-cols-4",
						// previewImageBase64s.length === 1 && "grid-cols-1",
						// previewImageBase64s.length === 2 && "grid-cols-2",
						// previewImageBase64s.length > 2 && "grid-cols-2 md:grid-cols-4",
					)}
				>
					{previewImageBase64s.map((base64, index) => (
						<div
							key={index}
							className="group relative mx-auto flex aspect-square h-full w-full max-w-md items-center justify-center rounded-lg bg-zinc-100 md:rounded-xl"
						>
							{base64 ? (
								<img
									src={base64}
									alt={`Generated image ${index + 1}`}
									className="h-full w-full rounded-lg object-contain"
									onContextMenu={(e) => e.preventDefault()}
									onDragStart={(e) => e.preventDefault()}
								/>
							) : (
								submitting && (
									<p className="text-muted-foreground flex w-full flex-col items-center text-center">
										<LoaderIcon className="size-5 animate-spin" />
										<span className="font-mono text-sm tabular-nums">{seconds}s</span>
									</p>
								)
							)}
							<div className="absolute right-2 bottom-2 z-10 items-center gap-1">
								{base64 && (
									<Hint label="Download image">
										<div className="relative">
											<SubmitButton
												isSubmitting={downloading}
												disabled={!base64}
												size="icon"
												className="bg-foreground hover:bg-foreground/80"
												onClick={async () => {
													try {
														setDownloading(true);
														if (userHasPaid) {
															await downloadImageFromBase64(base64);
														} else {
															await downloadImageFromBase64WithWatermark(base64);
														}
													} catch (error) {
														console.error("Failed to download image:", error);
													} finally {
														setDownloading(false);
													}
												}}
											>
												<Download />
											</SubmitButton>
										</div>
									</Hint>
								)}
							</div>
						</div>
					))}
				</div>
			)}
		</div>
	);
}
