import { EMAIL_CONTACT, WEBDOMAIN, WEBNAME } from "@/lib/constants";
import { Separator } from "@/components/ui/separator";
import { NoPrefetchLink } from "@/components/ui/custom/no-prefetch-link";
import { Logo } from "../logo";

export interface RouteItem {
	name: string;
	href: string;
	icon?: React.ReactNode;
	target?: string;
	hidden?: boolean;
}

type FooterItem = {
	title: string;
	items: RouteItem[];
};

const footers: FooterItem[] = [
	{
		title: "Features",
		items: [
			{ name: "Image Generator", href: "/ai-image-generator" },
			{ name: "Image Editor", href: "/image-editor" },
			{ name: "Video Generator", href: "/ai-video-generator" },
			{ name: "Image Tools", href: "/image-tools" },
			{ name: "Video Tools", href: "/video-tools" },
			{ name: "AI Models", href: "/models" },
			{ name: "Discover", href: "/features" },
		],
	},
	{
		title: "Resources",
		items: [
			{ name: "Blog", href: "/#" },
			// { name: "Changelog", href: "/changelog" },
			{ name: "Pricing", href: "/pricing" },
		],
	},
	{
		title: "Company",
		items: [
			{ name: "Contact Us", href: `mailto:${EMAIL_CONTACT}` },
			{ name: "Terms", href: "/terms-of-use", target: "_blank" },
			{ name: "Privacy", href: "/privacy-policy", target: "_blank" },
		],
	},
];

export default function Footer() {
	return (
		<footer className="border-t pt-16">
			<div className="container flex flex-col gap-4 px-6">
				<div className="flex flex-col gap-8 md:flex-row">
					<div className="col-span-full flex w-full flex-col items-start md:col-span-2 md:max-w-[260px] lg:max-w-[360px] xl:max-w-[380px]">
						<div className="space-y-4">
							<div className="flex items-center space-x-2">
								<Logo className="size-8" />
								<span className="font-heading text-xl font-semibold">{WEBNAME}</span>
							</div>
							<p className="text-muted-foreground py-2 text-sm leading-relaxed md:pr-12">Turn your ideas into reality.</p>
						</div>
					</div>
					<div className="w-full columns-2 space-y-8 sm:columns-2 md:columns-3 lg:columns-3">
						{footers.map((footer, index) => (
							<div key={index} className="overflow-hidden text-start text-[13px]">
								<p className="mb-4 font-medium uppercase">{footer.title}</p>
								<ul className="text-muted-foreground space-y-2 font-normal">
									{footer.items.map((item, index) => (
										<li key={index} className={`${item.hidden ? "opacity-0" : ""}`}>
											<NoPrefetchLink
												className="hover:text-secondary-foreground text-muted-foreground text-sm"
												href={item.href}
												target={item.target}
											>
												{item.name}
											</NoPrefetchLink>
										</li>
									))}
								</ul>
							</div>
						))}
					</div>
				</div>

				<Separator />

				<div className="text-muted-foreground mb-6 text-xs">
					<div className="flex flex-col gap-4 text-center sm:flex-row sm:justify-between sm:text-left">
						<p className="mx-auto flex flex-row items-center gap-2 sm:mx-0">
							{/* <a href="https://www.pinterest.com/elliotroams/youstylize" target="_blank">
								<svg xmlns="http://www.w3.org/2000/svg" className="size-5" viewBox="0 0 24 24">
									<path
										fill="currentColor"
										d="M9.04 21.54c.96.29 1.93.46 2.96.46a10 10 0 0 0 10-10A10 10 0 0 0 12 2A10 10 0 0 0 2 12c0 4.25 2.67 7.9 6.44 9.34c-.09-.78-.18-2.07 0-2.96l1.15-4.94s-.29-.58-.29-1.5c0-1.38.86-2.41 1.84-2.41c.86 0 1.26.63 1.26 1.44c0 .86-.57 2.09-.86 3.27c-.17.98.52 1.84 1.52 1.84c1.78 0 3.16-1.9 3.16-4.58c0-2.4-1.72-4.04-4.19-4.04c-2.82 0-4.48 2.1-4.48 4.31c0 .86.28 1.73.74 2.3c.09.06.09.14.06.29l-.29 1.09c0 .17-.11.23-.28.11c-1.28-.56-2.02-2.38-2.02-3.85c0-3.16 2.24-6.03 6.56-6.03c3.44 0 6.12 2.47 6.12 5.75c0 3.44-2.13 6.2-5.18 6.2c-.97 0-1.92-.52-2.26-1.13l-.67 2.37c-.23.86-.86 2.01-1.29 2.7z"
									/>
								</svg>
							</a>
							<a href="https://github.com/elliotodyl/youstylize_community" target="_blank">
								<svg xmlns="http://www.w3.org/2000/svg" className="size-5" viewBox="0 0 24 24">
									<path
										fill="currentColor"
										d="M12 2A10 10 0 0 0 2 12c0 4.42 2.87 8.17 6.84 9.5c.5.08.66-.23.66-.5v-1.69c-2.77.6-3.36-1.34-3.36-1.34c-.46-1.16-1.11-1.47-1.11-1.47c-.91-.62.07-.6.07-.6c1 .07 1.53 1.03 1.53 1.03c.87 1.52 2.34 1.07 2.91.83c.09-.65.35-1.09.63-1.34c-2.22-.25-4.55-1.11-4.55-4.92c0-1.11.38-2 1.03-2.71c-.1-.25-.45-1.29.1-2.64c0 0 .84-.27 2.75 1.02c.79-.22 1.65-.33 2.5-.33s1.71.11 2.5.33c1.91-1.29 2.75-1.02 2.75-1.02c.55 1.35.2 2.39.1 2.64c.65.71 1.03 1.6 1.03 2.71c0 3.82-2.34 4.66-4.57 4.91c.36.31.69.92.69 1.85V21c0 .27.16.59.67.5C19.14 20.16 22 16.42 22 12A10 10 0 0 0 12 2"
									/>
								</svg>
							</a> */}
							<a href={`mailto:${EMAIL_CONTACT}`}>
								<svg xmlns="http://www.w3.org/2000/svg" className="size-5" viewBox="0 0 24 24">
									<path
										fill="currentColor"
										d="M22 6c0-1.1-.9-2-2-2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2zm-2 0l-8 5l-8-5zm0 12H4V8l8 5l8-5z"
									/>
								</svg>
							</a>
						</p>
						<p className="text-sm sm:order-first">
							&copy; {new Date().getFullYear()} {WEBNAME}.art. All rights reserved.
						</p>
					</div>
				</div>
			</div>
		</footer>
	);
}
