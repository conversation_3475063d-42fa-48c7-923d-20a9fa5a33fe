"use client";

import React, { useState } from "react";
import {
	ChevronDown,
	CreditCard,
	History,
	CoinsIcon,
	MailIcon,
	VideoIcon,
	ImagePlusIcon,
	GemIcon,
	LogOutIcon,
	XIcon,
	GalleryHorizontalEndIcon,
	FolderIcon,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { Logo } from "@/components/logo";
import { useSession } from "@/lib/auth-client";
import { EMAIL_CONTACT, WEBNAME } from "@/lib/constants";
import { Separator } from "@/components/ui/separator";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import {
	NavigationMenu,
	NavigationMenuContent,
	NavigationMenuItem,
	NavigationMenuLink,
	NavigationMenuList,
	NavigationMenuTrigger,
} from "@/components/ui/navigation-menu";
import { useSignInBoxOpenStore } from "@/store/useSignInBoxOpenStore";
import { DropdownMenu, DropdownMenuContent, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useUserStore } from "@/store/useUserStore";
import { MembershipID } from "@/@types/membership-type";
import { usePlanBoxOpenStore } from "@/store/usePlanBoxOpenStore";
import { useSignOut } from "@/hooks/use-signout";
import { NoPrefetchLink } from "../ui/custom/no-prefetch-link";
import { BannerTop } from "../shared/banner-top";
import { Badge } from "../ui/badge";
import { EVENT_OPEN_PLAN_ID } from "@/lib/track-events";
import { Sheet, SheetClose, SheetContent, SheetHeader, SheetTitle } from "@/components/ui/sheet";
import { Icons } from "../icon/icons";

interface MenuItem {
	name: string;
	href?: string;
	icon?: React.ReactNode;
	target?: string;
	hidden?: boolean;
	items?: {
		name: string;
		href: string;
		description?: string;
		new?: boolean;
	}[];
}

const menuItems: MenuItem[] = [
	{ name: "Image Generator", href: "/ai-image-generator" },
	{ name: "Image Editor", href: "/image-editor" },
	{ name: "Video Generator", href: "/ai-video-generator" },
	// {
	// 	name: "Image AI",
	// 	items: [
	// 		{ name: "Flux", href: "/flux" },
	// 		{
	// 			name: "Flux Kontext",
	// 			href: "/flux/flux-kontext",
	// 		},
	// 		{
	// 			name: "Flux Krea",
	// 			href: "/flux/flux-krea",
	// 			new: true,
	// 		},
	// 		{ name: "Imagen 4", href: "/imagen" },
	// 		{ name: "Ideogram 3", href: "/ideogram" },
	// 		{ name: "Recraft 3", href: "/recraft" },
	// 		{ name: "Seedream 3", href: "/ai-image-generator?model=seedream-3" },
	// 		{ name: "HiDream", href: "/hidream" },
	// 		{ name: "Gemini Flash", href: "/gemini" },
	// 	],
	// },
	// { name: "Blog", href: "/blog" },
	{ name: "Pricing", href: "/pricing" },
];

export const Header = () => {
	const { data: session } = useSession();
	const { setSignInBoxOpen } = useSignInBoxOpenStore();
	const { setPlanBoxOpen } = usePlanBoxOpenStore();
	const { handleSignOut } = useSignOut();
	const { user, creditsAll: userCreditsAll, hasPaid: userHasPaid } = useUserStore();

	const [showMobileMenu, setShowMobileMenu] = useState<boolean>(false);

	const userMenu = () => {
		return (
			<DropdownMenu modal={false}>
				<DropdownMenuTrigger asChild className="cursor-pointer">
					<div className="flex shrink-0 flex-row items-center gap-2">
						<Avatar className="h-7 w-7">
							<AvatarImage src={session?.user.image!} alt="User Avatar" />
							<AvatarFallback>{session?.user.name}</AvatarFallback>
						</Avatar>
						<ChevronDown className="text-muted-foreground size-3.5 shrink-0 lg:hidden" />
					</div>
				</DropdownMenuTrigger>
				<DropdownMenuContent className="w-[280px] p-0" align="end" forceMount>
					<div className="flex gap-5 p-5">
						<Avatar className="flex size-9 shrink-0 items-center gap-2 text-zinc-800">
							<AvatarImage src={session?.user.image!} alt="User Avatar" />
							<AvatarFallback>{session?.user.name}</AvatarFallback>
						</Avatar>
						<div className="flex min-w-0 flex-1 flex-col items-start">
							<p className="truncate text-sm font-semibold">{session?.user.name ?? WEBNAME}</p>
							<p className="text-muted-foreground mt-1 truncate text-xs">{session?.user.email ?? "--"}</p>
						</div>
					</div>
					{user?.membershipId === MembershipID.Free && (
						<div className="px-[24px] pb-5">
							<Button size="sm" className="bg-action hover:bg-action/80 w-full" onClick={() => setPlanBoxOpen(true)}>
								Get a plan
							</Button>
						</div>
					)}
					<Separator />

					<NoPrefetchLink
						href="/ai-image-generator"
						className="hover:bg-muted flex h-11 w-full cursor-pointer flex-row items-center justify-between px-[24px] text-xs transition-all duration-100"
					>
						<div className="flex flex-row items-center">
							<ImagePlusIcon className="mr-5 h-4 w-4 shrink-0" />
							Image Generator
						</div>
					</NoPrefetchLink>

					<NoPrefetchLink
						href="/ai-video-generator"
						className="hover:bg-muted flex h-11 w-full cursor-pointer flex-row items-center justify-between px-[24px] text-xs transition-all duration-100"
					>
						<div className="flex flex-row items-center">
							<VideoIcon className="mr-5 h-4 w-4 shrink-0" />
							Video Generator
						</div>
					</NoPrefetchLink>

					<Separator />

					<NoPrefetchLink
						href="/assets"
						className="hover:bg-muted flex h-11 w-full cursor-pointer flex-row items-center justify-between px-[24px] text-xs transition-all duration-100"
					>
						<div className="flex flex-row items-center">
							<FolderIcon className="mr-5 h-4 w-4 shrink-0" />
							My Assets
						</div>
					</NoPrefetchLink>
					<NoPrefetchLink
						href="/user/my-billing"
						className="hover:bg-muted flex h-11 w-full cursor-pointer flex-row items-center justify-between px-[24px] text-xs transition-all duration-100"
					>
						<div className="flex flex-row items-center">
							<CreditCard className="mr-5 h-4 w-4 shrink-0" />
							My Billing
						</div>
						<p className="flex items-center gap-1 rounded-sm bg-zinc-700 px-2 py-1 text-xs font-medium text-zinc-100">
							{user?.membershipFormatted}
						</p>
					</NoPrefetchLink>
					<a
						href={`mailto:${EMAIL_CONTACT}`}
						// target="_blank"
						className="hover:bg-muted flex h-11 w-full cursor-pointer flex-row items-center justify-between px-[24px] text-xs transition-all duration-100"
					>
						<div className="flex flex-row items-center">
							<MailIcon className="mr-5 h-4 w-4 shrink-0" />
							Contact Us
						</div>
					</a>

					<Separator />

					<button
						className="hover:bg-muted flex h-11 w-full cursor-pointer items-center px-[24px] text-xs transition-all duration-100"
						onClick={handleSignOut}
					>
						<LogOutIcon className="mr-5 h-4 w-4 shrink-0" />
						Sign out
					</button>
				</DropdownMenuContent>
			</DropdownMenu>
		);
	};

	return (
		<header className="bg-background sticky top-0 z-20 w-full border-b transition-colors duration-300">
			{/* <BannerTop /> */}
			<div className="flex h-14 flex-wrap items-center justify-between px-6">
				<div className="flex flex-row items-center gap-8">
					<button className="-mr-2 flex cursor-pointer lg:hidden" onClick={() => setShowMobileMenu(true)}>
						<Icons.SidebarMynaUI className="text-muted-foreground/80 size-5" />
					</button>
					<NoPrefetchLink href="/" className="hidden items-center gap-2 lg:flex rtl:space-x-reverse">
						<Logo className="rounded" />
						<span className="hidden text-xl font-semibold md:block">{WEBNAME}</span>
					</NoPrefetchLink>
					<div className="hidden rounded-lg font-normal md:flex-row lg:flex">
						<NavigationMenu viewport={false}>
							<NavigationMenuList className="space-x-0">
								{menuItems.map((route, index) => (
									<React.Fragment key={index}>
										{route.items ? (
											<NavigationMenuItem>
												<NavigationMenuTrigger className="bg-transparent px-3 font-normal hover:bg-transparent focus:bg-transparent data-active:bg-transparent data-[state=open]:bg-transparent">
													{route.name}
												</NavigationMenuTrigger>
												<NavigationMenuContent>
													<div className="space-y-2">
														{/* <p className="text-sm font-medium">{route.name}</p> */}
														<div className="flex w-[240px] flex-col gap-0.5">
															{route.items.map((feature, index) => (
																<NavigationMenuLink key={index} asChild>
																	<NoPrefetchLink href={feature.href}>
																		<div className="flex flex-row items-center">
																			{feature.name}
																			{feature.new && (
																				<Badge
																					variant="secondary"
																					className="ml-1 rounded-full bg-green-500 px-1.5 py-0.5 text-[10px]"
																				>
																					New
																				</Badge>
																			)}
																		</div>
																		{feature.description && (
																			<div className="text-muted-foreground">{feature.description}</div>
																		)}
																	</NoPrefetchLink>
																</NavigationMenuLink>
															))}
														</div>
													</div>
												</NavigationMenuContent>
											</NavigationMenuItem>
										) : (
											<NavigationMenuItem>
												<NavigationMenuLink className={cn("hover:text-accent-foreground px-3 py-2")} asChild>
													<NoPrefetchLink href={route.href!} className="flex flex-row items-center" target={route.target}>
														{route.name}
														{route.icon && <>{route.icon}</>}
													</NoPrefetchLink>
												</NavigationMenuLink>
											</NavigationMenuItem>
										)}
									</React.Fragment>
								))}
							</NavigationMenuList>
						</NavigationMenu>
					</div>
				</div>

				<div className="flex flex-row items-center gap-2">
					{session ? (
						<>
							{user?.membershipId !== MembershipID.Free && (
								<Button
									id={EVENT_OPEN_PLAN_ID}
									size="sm"
									className="bg-action hover:bg-action/80 h-7 cursor-pointer rounded-sm text-[13px] font-normal"
									onClick={() => setPlanBoxOpen(true)}
								>
									<GemIcon className="size-3.5" />
									Upgrade
								</Button>
							)}
							<div className="bg-muted flex h-7 flex-row items-center gap-1 rounded-md px-3 py-1.5 text-sm text-green-500">
								<CoinsIcon className="size-4" /> <span>{userCreditsAll}</span>
							</div>
							<div className="hidden lg:block">{userMenu()}</div>
						</>
					) : (
						<Button size="sm" onClick={() => setSignInBoxOpen(true)} className="bg-foreground hover:bg-foreground/80">
							Sign In
						</Button>
					)}
				</div>
			</div>

			<Sheet open={showMobileMenu} onOpenChange={setShowMobileMenu}>
				<SheetContent side="left" className="w-5/6">
					<SheetHeader>
						<SheetTitle>
							<div className="flex flex-row justify-between">
								<SheetClose className="ring-offset-background focus:ring-ring data-[state=open]:bg-secondary rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none">
									<XIcon className="text-foreground size-5" />
									<span className="sr-only">Close</span>
								</SheetClose>
								{session ? (
									userMenu()
								) : (
									<Button size="sm" onClick={() => setSignInBoxOpen(true)} className="bg-foreground hover:bg-foreground/80">
										Sign In
									</Button>
								)}
							</div>
							<NoPrefetchLink href="/" className="mt-4 flex items-center space-x-2">
								<Logo className="size-8" />
								<span className="text-lg font-semibold">{WEBNAME}</span>
							</NoPrefetchLink>
						</SheetTitle>
						<div className="h-full divide-y text-start">
							{menuItems.map((route, index) => (
								<React.Fragment key={index}>
									{route.items ? (
										<div className="space-y-2">
											<Accordion type="single" collapsible>
												<AccordionItem value="item-1">
													<AccordionTrigger className="py-3 text-base font-normal hover:no-underline">{route.name}</AccordionTrigger>
													<AccordionContent className="space-y-2">
														{route.items.map((route, index) => (
															<div key={index} className="text-muted-foreground">
																<NoPrefetchLink href={route.href} className="" onClick={() => setShowMobileMenu(false)}>
																	<p className="items-center">{route.name}</p>
																</NoPrefetchLink>
															</div>
														))}
													</AccordionContent>
												</AccordionItem>
											</Accordion>
										</div>
									) : (
										<div className="py-3">
											<NoPrefetchLink
												href={route.href!}
												className="font-normal"
												target={route.target}
												onClick={() => setShowMobileMenu(false)}
											>
												<p className="items-center">
													{route.name}
													{route.icon && <>{route.icon}</>}
												</p>
											</NoPrefetchLink>
										</div>
									)}
								</React.Fragment>
							))}
						</div>
					</SheetHeader>
				</SheetContent>
			</Sheet>
		</header>
	);
};
