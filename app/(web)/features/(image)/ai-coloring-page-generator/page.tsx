import { OSS_URL_HOST, WEBNAME } from "@/lib/constants";
import type { Metadata } from "next";

export const metadata: Metadata = {
	title: `AI Coloring Page Generator | ${WEBNAME}`,
	description: "",
	alternates: {
		canonical: "/features/ai-coloring-page-generator",
	},
	robots: {
		index: false,
		follow: false,
	},
};

export default async function Page() {
	return (
		<main className="min-h-screen pb-20">
			{/* <div className="">
				<ImageGeneratorsClient
					title="AI Coloring Page Generator"
					defaultTool="ai-anime-generator"
					defaultPrompt="Clean line art of a smiling moon and happy stars, perfect for a coloring book page. Thick outlines and ample white space for easy coloring"
				/>
			</div> */}

			<section>
				<div className="relative pt-20 pb-20">
					<div className="mx-auto max-w-5xl px-6">
						<div className="text-center sm:mx-auto lg:mt-0 lg:mr-auto">
							<h1 className="mx-auto mt-8 max-w-4xl text-5xl font-semibold lg:mt-16">AI Coloring Page Generator</h1>
							<div className="text-muted-foreground mx-auto mt-4 text-lg">Create coloring page images with AI</div>
						</div>
					</div>
				</div>
			</section>
		</main>
	);
}
