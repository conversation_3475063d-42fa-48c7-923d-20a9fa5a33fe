import { OSS_URL_HOST, WEBNAME } from "@/lib/constants";
import type { Metadata } from "next";

export const metadata: Metadata = {
	title: `AI Logo Generator | ${WEBNAME}`,
	description: "",
	alternates: {
		canonical: "/features/ai-logo-generator",
	},
	robots: {
		index: false,
		follow: false,
	},
};

export default async function Page() {
	return (
		<main className="min-h-screen pb-20">
			{/* <div className="">
				<ImageGeneratorsClient
					title="AI Logo Generator"
					defaultTool="ai-logo-generator"
					defaultModelId={IDEOGRAM_3_TURBO.id}
					defaultModelStyle="DESIGN"
					defaultPrompt='logo name: [NAME]. Design Brief: Looking for a clean, modern, tech logo for startup company. Would like a logo icon as well as the word "[NAME]" in the logo. Would like to be able to use them together, or separately and still make sense. Colors will be Black on white and maybe some red accent in alternate look. Current logo attached for start.'
				/>
			</div> */}

			<section>
				<div className="relative pt-20 pb-20">
					<div className="mx-auto max-w-5xl px-6">
						<div className="text-center sm:mx-auto lg:mt-0 lg:mr-auto">
							<h1 className="mx-auto mt-8 max-w-4xl text-5xl font-semibold lg:mt-16">AI Logo Generator</h1>
							<div className="text-muted-foreground mx-auto mt-4 text-lg">Create Logo with AI</div>
						</div>
					</div>
				</div>
			</section>
		</main>
	);
}
