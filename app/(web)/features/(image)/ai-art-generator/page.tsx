import { OSS_URL_HOST, WEBNAME } from "@/lib/constants";
import type { Metadata } from "next";

export const metadata: Metadata = {
	title: `AI Art Generator | ${WEBNAME}`,
	description: "",
	alternates: {
		canonical: "/features/ai-art-generator",
	},
	robots: {
		index: false,
		follow: false,
	},
};

export default async function Page() {
	return (
		<main className="min-h-screen pb-20">
			{/* <div className="">
				<ImageGeneratorsClient
					title="AI Art Generator"
					defaultTool="ai-art-generator"
					defaultPrompt="Abstract cubist artwork of a jazz musician playing the saxophone, featuring cubist shapes and lines, swirling patterns, a harmonious color palette, and a vintage feel. The digital art style results in a high-resolution image."
				/>
			</div> */}

			<section>
				<div className="relative pt-20 pb-20">
					<div className="mx-auto max-w-5xl px-6">
						<div className="text-center sm:mx-auto lg:mt-0 lg:mr-auto">
							<h1 className="mx-auto mt-8 max-w-4xl text-5xl font-semibold lg:mt-16">AI Art Generator</h1>
							<div className="text-muted-foreground mx-auto mt-4 text-lg">Create AI art images with AI</div>
						</div>
					</div>
				</div>
			</section>
		</main>
	);
}
