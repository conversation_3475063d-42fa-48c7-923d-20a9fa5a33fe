import { Example } from "@/components/landing/example";
import { OSS_URL_HOST, WEBNAME } from "@/lib/constants";
import type { Metadata } from "next";
// import { Announcement, AnnouncementTag, AnnouncementTitle } from "@/components/ui/kibo-ui/announcement";
import FAQsComponent from "@/components/landing/faqs";
import BreadcrumbPath from "@/components/landing/breadcrumb-path";
import { House } from "lucide-react";
import PageImageClient from "../_components/page-image.client";
import { IMAGEN_4_PREVIEW_FAST } from "@/config/image-models-config";

export const metadata: Metadata = {
	title: `Imagen 4: Try Google AI Image Generator for Free | ${WEBNAME}`,
	description:
		"Discover Imagen 4, Google’s latest AI image generator. Turn text into stunning, high-quality visuals in seconds. Try Imagen 4 now and explore the future of creative image generation!",
	alternates: {
		canonical: "/models/image/imagen",
	},
};

export default async function Page() {
	return (
		<main className="min-h-screen pb-20">
			<div className="px-6 pt-3">
				<BreadcrumbPath
					paths={[
						{ title: "Home", href: "/", icon: House },
						{ title: "AI Image Generator", href: "/ai-image-generator" },
					]}
					current={"Imagen"}
				/>
			</div>

			<section>
				<div className="relative pt-8 pb-16">
					<div className="mx-auto max-w-5xl px-6">
						<div className="mt-8 space-y-4 text-center sm:mx-auto lg:mr-auto">
							{/* <Announcement themed className="bg-emerald-100 text-emerald-700">
								<AnnouncementTag className="group-[.announcement-themed]:bg-white/40">Latest update</AnnouncementTag>
								<AnnouncementTitle>Imagen 4 is now available!</AnnouncementTitle>
							</Announcement> */}
							<h1 className="mx-auto max-w-4xl text-5xl font-semibold">Imagen 4 AI Image Generator</h1>
							<div className="text-muted-foreground mx-auto">
								Imagen is a powerful AI image generation tool from Google that transforms text prompts into realistic, high-quality visuals in
								seconds. The Imagen 4 version was released in May 2025 and offers even better image quality, improved speed, and a simple user
								interface. Take your creative projects to the next level—try Imagen 4 today and experience the future of AI-powered image
								creation!
							</div>
						</div>
					</div>
				</div>
			</section>

			<div className="container max-w-4xl pb-20">
				<PageImageClient defaultModelId={IMAGEN_4_PREVIEW_FAST.id} defaultGenType="text-to-image" />
			</div>

			<Example
				title="Get Inspired"
				description="Get inspired by what others are creating with Image 4"
				// className="-mt-16"
				images={[
					{
						url: `${OSS_URL_HOST}mkt/pages/image-model/imagen/imagen4/example_1.webp`,
						prompt: 'The text "Imagen-4" in front of a photo, center middle. The photo: Create a cinematic, photorealistic medium shot capturing the nostalgic warmth of a late 90s indie film. The focus is a young woman with brightly dyed pink-gold hair and freckled skin, looking directly and intently into the camera lens with a hopeful yet slightly uncertain smile, she is slightly off-center. She wears an oversized, vintage band t-shirt that says "Dreampik.art" (slightly worn) over a long-sleeved striped top and simple silver stud earrings. The lighting is soft, golden hour sunlight streaming through a slightly dusty window, creating lens flare and illuminating dust motes in the air. The background shows a blurred, cluttered bedroom with posters on the wall and fairy lights, rendered with a shallow depth of field. Natural film grain, a warm, slightly muted color palette, and sharp focus on her expressive eyes enhance the intimate, authentic feel',
					},
					{
						url: `${OSS_URL_HOST}mkt/pages/image-model/imagen/imagen4/example_2.webp`,
						prompt: "Beach scene with orange-striped umbrellas and chairs, hanging oranges, and a calm sea under a clear blue sky.",
					},
					{
						url: `${OSS_URL_HOST}mkt/pages/image-model/imagen/imagen4/example_3.webp`,
						prompt: "A pair of AirPods Max reimagined in a Rococo aesthetic, featuring pastel pink ear cups adorned with delicate pearl patterns and golden filigree swirls. The headband is wrapped in soft satin ribbon, tied into an elegant bow at the top. The headphones rest on a velvet pastel green cushion, surrounded by scattered pearls, miniature golden cherubs, and a backdrop of flowing tulle and softly glowing light",
					},
					{
						url: `${OSS_URL_HOST}mkt/pages/image-model/imagen/imagen4/example_4.webp`,
						prompt: 'A high-resolution photograph of a gold coin featuring the Google logo at the center. The coin should have the year 1998 engraved at the top. Include finely detailed engravings, ornamental border patterns, and authentic coin textures like reeded edges, matte background, and polished raised elements. Add inscriptions of "Google" near the bottom in bold lettering. The design should look like a professionally minted commemorative coin with symmetrical layout, precision detailing, and classic metallic shading, presented on a dark backdrop for contrast.',
					},
					{
						url: `${OSS_URL_HOST}mkt/pages/image-model/imagen/imagen4/example_5.webp`,
						prompt: "Whimsical pink balloons with smiling faces float above a building against a clear blue sky, creating a playful urban scene.",
					},
					{
						url: `${OSS_URL_HOST}mkt/pages/image-model/imagen/imagen4/example_6.webp`,
						prompt: "Blurred city street scene with people walking and cars moving, capturing the dynamic energy of urban life.",
					},
					{
						url: `${OSS_URL_HOST}mkt/pages/image-model/imagen/imagen4/example_7.webp`,
						prompt: 'Capture an intimate close-up bathed in warm, soft, late-afternoon sunlight filtering into a quintessential 1960s kitchen. The focal point is a charmingly designed vintage package of all-purpose flour, resting invitingly on a speckled Formica countertop. The packaging itself evokes pure nostalgia: perhaps thick, slightly textured paper in a warm cream tone, adorned with simple, bold typography (a friendly serif or script) in classic red and blue “ALL-PURPOSE FLOUR”, featuring a delightful illustration like a stylized sheaf of wheat or a cheerful baker character. In smaller bold print at the bottom of the package: “NET WT 5 LBS (80 OZ) 2.27kg”. Focus sharply on the package details – the slightly soft edges of the paper bag, the texture of the vintage printing, the inviting "All-Purpose Flour" text. Subtle hints of the 1960s kitchen frame the shot – the chrome edge of the counter gleaming softly, a blurred glimpse of a pastel yellow ceramic tile backsplash, or the corner of a vintage metal canister set just out of focus. The shallow depth of field keeps attention locked on the beautifully designed package, creating an aesthetic rich in warmth, authenticity, and nostalgic appeal.',
					},
					{
						url: `${OSS_URL_HOST}mkt/pages/image-model/imagen/imagen4/example_8.webp`,
						prompt: "A hyper-realistic scene of Minecraft’s Steve jumping mid-air over car rooftops in a busy street in real-life New York City, modern cars, blurred motion, urban chaos, traffic, reflections on vehicles, dynamic action pose, his blocky body casting shadow, chased by a tall terrifying Enderman emerging from a shadowy alley behind him, dramatic lighting, gritty cinematic atmosphere, real buildings and traffic lights, dusk sky, cinematic lens blur, photorealistic style",
					},
				]}
			/>

			<section className="py-20">
				<div className="container px-6">
					<h2 className="mb-12 text-center text-3xl font-semibold">Imagen Model Variants</h2>

					<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
						{/* Imagen 4 Card */}
						<div className="bg-muted rounded-xl p-4">
							<div className="mb-3 flex items-center justify-between">
								<h3 className="text-primary text-lg font-semibold">Imagen 4</h3>
								<span className="rounded-full bg-blue-100 px-2.5 py-1 text-sm font-medium text-blue-800">May 2025</span>
							</div>
							<div className="text-secondary-foreground">
								<ul className="list-inside list-disc space-y-1 text-sm">
									<li>High-resolution images up to 2K, multiple aspect ratios</li>
									<li>Exceptional detail: clear text, fabrics, water, fur, and human anatomy</li>
									<li>Improved and accurate typography rendering</li>
									<li>Based on Gemini 2.5 architecture for better quality and scene complexity</li>
								</ul>
							</div>
						</div>
						{/* Imagen 3 Card */}
						<div className="bg-muted rounded-xl p-4">
							<div className="mb-3 flex items-center justify-between">
								<h3 className="text-primary text-lg font-semibold">Imagen 3</h3>
								<span className="rounded-full bg-indigo-100 px-2.5 py-1 text-sm font-medium text-indigo-800">May 2024</span>
							</div>
							<div className="text-secondary-foreground">
								<ul className="list-inside list-disc space-y-1 text-sm">
									<li>Photorealistic image generation with rich detail and lighting.</li>
									<li>Advanced prompt comprehension for accurate visuals.</li>
									<li>Versatile styles from realism to fantasy.</li>
									<li>Superior prompt-to-image quality compared to competitors</li>
								</ul>
							</div>
						</div>
					</div>
				</div>
			</section>

			<FAQsComponent
				title="Imagen 4 Related FAQs"
				faqs={[
					{
						question: "What is Imagen 4 and how is it different from previous versions?",
						answer: "Imagen 4 is the latest iteration of Google’s cutting-edge AI image generator. Compared to earlier versions, Imagen 4 features significant improvements in visual realism, image clarity, and overall output quality. It uses an upgraded model that can interpret complex prompts more accurately and create even more lifelike and detailed images, making it ideal for both creative projects and professional work. This version also supports more customization, offering users the ability to create highly specific visual styles and aesthetics with ease.",
					},
					{
						question: "Which versions does Google Imagen 4 support?",
						answer: "Google Imagen 4 supports three main versions:\n- Imagen 4: The standard model, offering high-quality, photorealistic image generation with improved text rendering and fine detail.\n- Imagen 4 Fast: Designed for speed, this version generates images much faster while maintaining about 90% of the standard model's quality. Ideal for quick-turnaround creative projects.\n- Imagen 4 Ultra: Focused on delivering the highest quality and realism in images, perfect for professional and detailed work.\nAll versions are currently in preview and can be accessed through platforms like Gemini, Vertex AI, and Google Workspace apps. This flexibility allows users to choose the version that best fits their needs for speed, quality, or a balance of both.",
					},
					{
						question: "Is Imagen AI free to use?",
						answer: "Yes, you can use Imagen AI for free on Dreampik.art. We provide users with a free image generation quota, allowing you to create stunning visuals without any financial commitment. You don't need to enter any credit card details or purchase add-ons to access these features. It’s a hassle-free way to experience the power of AI-driven creativity without hidden costs.",
					},
					{
						question: "What are the key advantages of using Imagen?",
						answer: "Imagen is known for its speed, accuracy, and high-quality results. Key advantages include: 1. Rapid Image Generation: It produces high-quality images in just a few seconds. 2. Customizability: Users can fine-tune the images based on detailed prompts, allowing for very specific visual outcomes. 3. Google-Backed AI: As part of Google’s advanced AI ecosystem, Imagen utilizes state-of-the-art machine learning technology, ensuring the highest level of precision in image creation. 4. Versatility: Whether you need realistic or creative art, Imagen delivers a broad range of styles and quality, making it versatile for everything from personal projects to professional design needs.",
					},
					{
						question: "Is Imagen suitable for professional use in creative industries?",
						answer: "Yes, Imagen is highly suitable for professional use in creative industries. Its ability to generate high-resolution, photorealistic images, combined with its quick turnaround time, makes it an excellent tool for designers, content creators, marketers, and visual artists. Whether you're working on advertising campaigns, social media content, game design, or even conceptual art, Imagen offers the flexibility and quality needed to meet professional standards. Its ease of use means you don’t need to be an expert in AI to achieve impressive results, but the technology is advanced enough to satisfy industry professionals.",
					},
					{
						question: "What makes Imagen stand out from other image generators?",
						answer: "Imagen stands out from other image generators for several reasons: 1. Superior Image Quality: Powered by Google’s advanced AI, Imagen produces highly realistic and visually captivating images. The level of detail is unmatched compared to many other tools on the market. 2. Customizability: Unlike some AI tools that produce generic outputs, Imagen allows for in-depth control over the style and details of the image, making it suitable for a wide range of projects. 3. Google’s AI Power: Backed by Google’s technology, Imagen benefits from some of the most advanced machine learning models available, which ensures the results are not only fast but highly accurate.",
					},
					{
						question: "Is Imagen AI easy to use?",
						answer: "Yes, Imagen AI is designed to be incredibly easy to use. Whether you're a beginner or an experienced designer, you can start generating images right away by simply typing in a text prompt. The interface is intuitive, with no technical skills required. Once you input your prompt, Imagen quickly translates it into high-quality images. You don’t need any prior experience with AI or graphic design software to make the most out of Imagen—just a clear idea of what you want the image to look like.",
					},
					{
						question: "How fast can I generate images with Google Imagen AI?",
						answer: "Google Imagen AI is remarkably fast, and image generation typically takes only a few seconds. This quick turnaround is perfect for users who need high-quality images on short notice. Whether you’re working on a tight deadline or experimenting with different concepts, you can rely on Imagen to deliver your desired results in a fraction of the time it would take with traditional design methods.",
					},
					{
						question: "Can I use Google Imagen AI?",
						answer: "Yes, absolutely! Anyone can use Google Imagen AI, and it’s particularly easy to access through Dreampik.art. You don’t need any special technical skills or software to get started. Simply visit the platform, enter your prompt, and you’re good to go. Plus, Dreampik.art offers a free image generation quota, so you can begin experimenting with AI-generated art without any costs or hidden fees. It’s accessible to everyone, from beginners to professionals, and offers a seamless experience for all users.",
					},
				]}
			/>

			{/* <FinalCTA
				ctaText="Try YouStylize for Free"
				ctaUrl="/studio-ghibli"
				title="Turn Your Photos Into Magical Art"
				description="Turn your everyday snapshots into stunning works of art with YouStylize! Our AI-powered style conversion creates unique, eye-catching portraits in just a few clicks. Unleash your creativity—try it now!"
			/> */}
		</main>
	);
}
