import { OSS_URL_HOST, WEBNAME } from "@/lib/constants";
import type { Metadata } from "next";
import BreadcrumbPath from "@/components/landing/breadcrumb-path";
import FinalCTA from "@/components/landing/final-cta";
import FAQsComponent from "@/components/landing/faqs";
import { GridSections } from "@/components/landing/grid-sections";
import { Globe, Image, Palette, Rocket, Sparkle, Sparkles, TerminalIcon, Wand } from "lucide-react";
import PageImageClient from "../../_components/page-image.client";
import { HIDREAM_E1_1 } from "@/config/image-models-config";

export const metadata: Metadata = {
	title: `HiDream E1.1 Free: Try AI image editing with HiDream E1.1 | ${WEBNAME}`,
	description:
		"Discover HiDream E1.1, the AI-powered image editing tool that helps you easily transform and enhance your photos. Experience smart, creative editing—try HiDream E1.1 for free.",
	alternates: {
		canonical: "/models/image/hidream/hidream-e1",
	},
};

export default async function Page() {
	return (
		<main className="min-h-screen">
			<div className="px-6 pt-3">
				<BreadcrumbPath
					paths={[
						{ title: "Home", href: "/" },
						{ title: "AI Image Generator", href: "/ai-image-generator" },
						{ title: "HiDream", href: "/models/image/hidream" },
					]}
					current={"HiDream E1"}
				/>
			</div>

			<section>
				<div className="relative pt-20 pb-16">
					<div className="mx-auto max-w-5xl px-6">
						<div className="text-center sm:mx-auto lg:mt-0 lg:mr-auto">
							<h1 className="mx-auto mt-8 max-w-4xl text-5xl font-semibold">Edit Images with HiDream E1.1</h1>
							<div className="text-muted-foreground mx-auto mt-4">
								HiDream E1.1 is an advanced AI image editing tool that lets you transform and enhance your photos with simple instructions.
								Whether you want to change styles, refine details, or get creative, HiDream E1 makes editing easy and powerful. Try for free
								today.
							</div>
						</div>
					</div>
				</div>
			</section>

			<div className="container max-w-4xl pb-20">
				<PageImageClient defaultModelId={HIDREAM_E1_1.id} defaultGenType="image-to-image" />
			</div>

			<div className="container flex max-w-4xl flex-col items-center gap-16 px-6 py-20">
				<div className="text-center">
					<h2 className="text-[32px] font-semibold text-pretty">HiDream E1.1 Image Editing Examples</h2>
					<p className="text-muted-foreground mx-auto mt-4 max-w-4xl text-pretty">Get inspired by what others are creating with Dreampik</p>
				</div>
				<div className="w-full space-y-8">
					{[
						{
							imageBefore: `${OSS_URL_HOST}mkt/pages/image-model/hidream/hidream-e1/example-1-before.webp`,
							imageAfter: `${OSS_URL_HOST}mkt/pages/image-model/hidream/hidream-e1/example-1-after.webp`,
							prompt: "Replace the plate with a wooden plate",
						},
						{
							imageBefore: `${OSS_URL_HOST}mkt/pages/image-model/hidream/hidream-e1/example-2-before.webp`,
							imageAfter: `${OSS_URL_HOST}mkt/pages/image-model/hidream/hidream-e1/example-2-after.webp`,
							prompt: "Convert the image into a 3D animated style.",
						},
						{
							imageBefore: `${OSS_URL_HOST}mkt/pages/image-model/hidream/hidream-e1/example-3-before.webp`,
							imageAfter: `${OSS_URL_HOST}mkt/pages/image-model/hidream/hidream-e1/example-3-after.webp`,
							prompt: "Add her a pair of glasses and a straw hat.",
						},
						{
							imageBefore: `${OSS_URL_HOST}mkt/pages/image-model/hidream/hidream-e1/example-4-before.webp`,
							imageAfter: `${OSS_URL_HOST}mkt/pages/image-model/hidream/hidream-e1/example-4-after.webp`,
							prompt: "Generate a formal ID photo based on this image, with a white background, black suit, and white shirt.",
						},
					].map((image, index) => (
						<div key={index} className="flex flex-row items-center gap-2 sm:gap-4 md:gap-8">
							<div className="group relative w-full break-inside-avoid overflow-hidden rounded-lg">
								<img src={image.imageBefore} alt="" className="aspect-square h-full w-full rounded-lg object-cover" loading="lazy" />
								<p className="bg-secondary-foreground/60 absolute top-2 left-2 rounded-sm px-2 py-1 text-xs text-white backdrop-blur-xs transition-all duration-300">
									Before
								</p>
								<div className="bg-secondary-foreground/30 text-muted absolute right-2 bottom-2 left-2 hidden rounded-md px-3 py-2.5 text-xs shadow-lg backdrop-blur-xs group-hover:block md:block md:text-sm">
									<p className="flex flex-row items-center gap-1.5 pb-1 font-medium">
										<TerminalIcon className="h-3.5 w-3.5" />
									</p>
									<p className="">{image.prompt}</p>
								</div>
							</div>
							<svg viewBox="0 0 144 61" fill="none" xmlns="http://www.w3.org/2000/svg" className="w-6 shrink-0 text-gray-400 sm:w-8 md:w-16">
								<path
									fillRule="evenodd"
									clipRule="evenodd"
									d="M68.9434 0.229236C58.3864 1.33176 45.4598 5.76533 36.6623 11.2545C33.1902 13.4126 29.155 16.3449 25.5421 19.3241C21.6478 22.5378 13.859 30.279 11.3487 33.4458C6.60979 39.3807 2.62156 46.6527 0.627451 52.9394C-0.146734 55.426 -0.217114 58.4286 0.486691 59.9533C1.21396 61.5719 1.6597 61.2904 1.6597 59.2496C1.6597 56.7865 2.3635 54.6049 4.56876 50.0541C6.96169 45.1279 8.55699 42.4771 11.513 38.5596C17.6361 30.4432 28.9673 20.5204 38.8441 14.6794C45.7413 10.5742 54.5389 7.47778 63.9464 5.81226C67.5124 5.17889 75.7938 4.94431 80.1809 5.3431C85.3421 5.81226 91.9579 7.40741 96.7672 9.28405C101.623 11.1842 107.559 14.4448 112.04 17.6586C114.925 19.6994 121.518 24.8836 121.541 25.0947C121.541 25.1651 120.087 25.6108 118.304 26.08C114.198 27.1356 112.626 27.7455 111.406 28.6604C110.139 29.6456 109.6 30.3963 109.436 31.4519C109.295 32.2025 109.389 32.4136 109.975 33.0236C111.125 34.173 111.993 34.173 120.814 33.0236C128.368 32.0618 128.978 32.0148 135.054 32.0148L141.412 31.9914L142.256 31.3346C143.383 30.4901 143.828 29.5752 143.828 28.2147C143.828 26.643 143.078 25.4701 141.154 24.0861C140.262 23.4292 138.667 22.139 137.635 21.2007C133.365 17.424 129.048 13.9287 127.289 12.8497C123.207 10.3162 120.204 10.1989 118.139 12.5212L117.436 13.3188L114.503 11.4422C105.119 5.48384 94.8904 1.7071 84.3802 0.323074C81.4008 -0.0522541 72.2279 -0.122634 68.9434 0.229236Z"
									fill="currentColor"
								></path>
							</svg>
							<div className="group relative w-full break-inside-avoid overflow-hidden rounded-lg">
								<img src={image.imageAfter} alt="" className="aspect-square h-full w-full rounded-lg object-cover" loading="lazy" />
								<p className="k bg-secondary-foreground/60 absolute top-2 left-2 rounded-sm px-2 py-1 text-xs text-white backdrop-blur-xs transition-all duration-300">
									After
								</p>
							</div>
						</div>
					))}
				</div>
			</div>

			<GridSections
				title="Why Choose HiDream E1.1?"
				description="HiDream E1.1 makes image editing simple, fast, and creative. Powered by advanced AI, it’s perfect for both professionals and everyday users who want high-quality results with ease."
				sections={[
					{
						title: "Smart Editing",
						description: "Just tell HiDream E1 what you want. The model understands your words and makes precise changes to your images.",
						icon: Wand,
					},
					{
						title: "Style Magic",
						description: "Switch your image style in seconds—cartoon, photorealistic, or artistic. HiDream E1 brings your vision to life",
						icon: Palette,
					},
					{
						title: "High Quality",
						description: "Enjoy crisp, detailed images every time. HiDream E1 supports high resolutions for professional results.",
						icon: Image,
					},
					{
						title: "Quick and Easy",
						description: "Get edits done in moments. HiDream E1 is built for speed, so you can focus on your creativity.",
						icon: Rocket,
					},
					{
						title: "Open & Free",
						description: "HiDream E1 is open-source and free for commercial use. Use it for any project, big or small.",
						icon: Globe,
					},
					{
						title: "All-in-One",
						description: "From adding elements to changing backgrounds, HiDream E1 handles all your editing needs in one place.",
						icon: Sparkles,
					},
				]}
			/>

			<FAQsComponent
				title="HiDream E1.1 Related FAQs"
				faqs={[
					{
						question: "What is HiDream E1?",
						answer: "HiDream E1 is an advanced, open-source AI model designed for instruction-based image editing. Developed by HiDream.ai and released in April 2025, it allows users to edit existing images using simple text instructions. Unlike traditional image generators, HiDream E1 focuses on modifying images, making it a powerful tool for creative professionals, developers, and anyone looking to enhance or transform their visuals.",
					},
					{
						question: "What are the key features of HiDream E1?",
						answer: "HiDream E1 stands out for its high-quality editing capabilities. It can handle a wide range of edits, including style changes, adding or removing elements, text modifications, and color adjustments. The model uses a unique instruction format for precise control and includes a refinement feature to improve output quality. HiDream E1 is open-source under the MIT license, making it free for both personal and commercial use.",
					},
					{
						question: "What types of projects is HiDream E1 best suited for?",
						answer: "HiDream E1 is ideal for creative projects that require advanced image editing. It’s perfect for artists, designers, and developers working on tasks like style transfer (for example, turning photos into Ghibli-style art), marketing visuals, content creation, and research. Its flexibility and open-source nature also make it a great choice for startups and businesses looking to integrate AI-powered editing into their products or workflows.",
					},
					{
						question: "What are some real-world use cases for HiDream E1?",
						answer: "HiDream E1 is used in a variety of scenarios, such as transforming photos into artistic illustrations, editing backgrounds for product images, adding or removing objects, and making local or global adjustments to images. Creative professionals use it for animation and art projects, while businesses leverage it for marketing and branding. Its interactive demo and command-line tools make it accessible for both quick edits and deeper integration into custom applications.",
					},
					{
						question: "Can I use HiDream E1 for free?",
						answer: "Yes! Dreampik offers a free quota for HiDream E1, so you can try our AI image editing features at no cost. Experience powerful and easy image editing before deciding to upgrade.",
					},
					{
						question: "How fast does HiDream E1 generate images?",
						answer: "HiDream E1 delivers results in just seconds. Our advanced AI model edits and refines your images quickly, saving you time and helping you stay creative.",
					},
				]}
			/>

			<FinalCTA
				ctaText="Try HiDream E1.1 Now"
				ctaUrl="#"
				title="Unlock Effortless Image Editing with HiDream E1"
				description="Experience the power of HiDream E1—an AI image editing tool that makes style changes and photo improvements simple for everyone. Whether you’re a creative professional or just want to enhance your pictures, HiDream E1 gives you fast, high-quality results with easy instructions."
			/>
		</main>
	);
}
