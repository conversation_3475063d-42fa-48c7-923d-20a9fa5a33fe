import { OSS_URL_HOST, WEBNAME } from "@/lib/constants";
import type { Metadata } from "next";
import BreadcrumbPath from "@/components/landing/breadcrumb-path";
import FinalCTA from "@/components/landing/final-cta";
import FAQsComponent from "@/components/landing/faqs";
import { House } from "lucide-react";
import FluxKontextExamples from "./flux-kontext-example.client";
import TweetGridServer from "@/components/landing/tweet-grid-server";
import PageImageClient from "../../_components/page-image.client";
import { FLUX_1_KONTEXT_DEV } from "@/config/image-models-config";

export const metadata: Metadata = {
	title: `Flux Kontext: Try Image Editing with Flux.1 Kontext for Free | ${WEBNAME}`,
	description:
		"Edit images with Flux Kontext. Use text or images to generate, refine, and style visuals. Available in Dev, Pro, and Max versions for all needs. Try free image editing now.",
	alternates: {
		canonical: "/models/image/flux/flux-kontext",
	},
};

export default async function Page() {
	return (
		<main className="min-h-screen">
			<div className="px-6 pt-3">
				<BreadcrumbPath
					paths={[
						{ title: "Home", href: "/", icon: House },
						{ title: "AI Image Generator", href: "/ai-image-generator" },
						{ title: "Flux", href: "/models/image/flux" },
					]}
					current={"Flux Kontext"}
				/>
			</div>

			<section>
				<div className="relative pt-16 pb-16">
					<div className="mx-auto max-w-5xl px-6">
						<div className="mt-8 text-center sm:mx-auto lg:mr-auto">
							<h1 className="mx-auto max-w-4xl text-5xl font-semibold">Edit Images with Flux Kontext</h1>
							<div className="text-muted-foreground mx-auto mt-4">
								Flux Kontext is a powerful suite for AI image editing——just use text prompts or images to create, refine, and style your
								visuals.
							</div>
						</div>
					</div>
				</div>
			</section>

			<div className="container max-w-4xl pb-20">
				<PageImageClient defaultModelId={FLUX_1_KONTEXT_DEV.id} defaultGenType="image-to-image" />
			</div>

			<div className="flex flex-col items-center gap-16 px-6 py-20">
				<div className="container text-center">
					<h2 className="text-[32px] font-semibold text-pretty">Flux Kontext Examples</h2>
					<p className="text-muted-foreground mx-auto mt-4 max-w-4xl text-pretty">Examples are generated using Flux Kontext models.</p>
				</div>
				<div className="container w-full space-y-8">
					<FluxKontextExamples
						examples={[
							{
								imageBefore: `${OSS_URL_HOST}mkt/pages/image-model/flux/flux-kontext/example-1-before.webp`,
								imageAfter: `${OSS_URL_HOST}mkt/pages/image-model/flux/flux-kontext/example-1-after.webp`,
								prompt: "Change the car color to red",
							},
							{
								imageBefore: `${OSS_URL_HOST}mkt/pages/image-model/flux/flux-kontext/example-2-before.webp`,
								imageAfter: `${OSS_URL_HOST}mkt/pages/image-model/flux/flux-kontext/example-2-after.webp`,
								prompt: "Replace 'joy' with 'Pik'",
							},
							{
								imageBefore: `${OSS_URL_HOST}mkt/pages/image-model/flux/flux-kontext/example-3-before.webp`,
								imageAfter: `${OSS_URL_HOST}mkt/pages/image-model/flux/flux-kontext/example-3-after.webp`,
								prompt: "Convert the woman to a rick and morty style character",
							},
						]}
					/>
				</div>
			</div>

			<TweetGridServer
				topic="Flux Kontext"
				tweets={[
					"1938257909726519640",
					"1938290349224743418",
					"1938259329498681385",
					"1932801239751758096",
					"1929192519310434604",
					"1929145202985341010",
					"1929578521149452534",
					"1929913716457316487",
				]}
			/>

			<FAQsComponent
				title="Flux Kontext Related FAQs"
				faqs={[
					{
						question: "What is Flux Kontext?",
						answer: "Flux Kontext is a powerful suite of AI image generation and editing models developed by Black Forest Labs. It lets users create new images from text descriptions or edit existing images using simple instructions. Flux Kontext stands out by combining both text prompts and image references, so you can easily make precise edits—like changing an object’s color or adding elements—while keeping key details, styles, or characters consistent. This makes it ideal for creative professionals who want fast, high-quality results without the complexity of manual design tools.",
					},
					{
						question: "Which models are included in Flux Kontext?",
						answer: "Flux Kontext offers three powerful AI models: [Dev], [Pro], and [Max]. The [Dev] model is designed for research, experimentation, and self-hosting, the [Pro] model provides high-quality image generation and editing with strong prompt following, and the [Max] model delivers premium performance with enhanced typography and even more accurate results. This selection lets users choose the best model for their creative and professional needs.",
					},
					{
						question: "What are the core capabilities of Flux Kontext?",
						answer: "Flux Kontext offers cutting-edge AI image generation and editing. Its core capabilities include: \n- generating high-quality images from text prompts;\n- editing existing images using simple language instructions;\n- maintaining consistency of characters and styles across different scenes;\n- making precise edits to specific parts of an image without affecting the rest;\n- mixing text and image inputs for flexible creative control;\n- and fast, cost-effective performance for both single edits and iterative workflows.\nThese features make Flux Kontext a powerful tool for creators, marketers, and designers who want to produce professional visual content quickly and easily.",
					},

					{
						question: "What can I do with Flux Kontext?",
						answer: "Flux Kontext lets you transform images using simple instructions. You can: \n- Change styles (switch photos to oil paintings or cartoon versions).\n- Edit text in images like signs and posters accurately.\n- Modify objects by changing colors, moving elements, or adding new items.\n- Keep characters consistent across different scenes for stories or branding.\n- Make targeted edits to specific areas without affecting the whole image.\nUse it to quickly create marketing visuals, concept art, or adapt content across regions while maintaining precision.",
					},
					{
						question: "How does Flux Kontext compare to other AI image models?",
						answer: "Flux Kontext beats alternatives like GPT-4o and BAGEL with precise, lightning-fast editing that maintains image context. While other models often regenerate entire scenes, it surgically edits only what you describe - changing colors, text, or styles in under 12 seconds without altering surrounding elements. Its unique character consistency, 8x faster processing, and lower costs make it ideal for marketing updates, design iterations, and content creation.",
					},
					{
						question: "What are the most common use cases for Flux Kontext?",
						answer: "Flux Kontext excels at creative and professional tasks where speed and precision matter. Top use cases include:\n- 1. Creative design & advertising - generate concepts for campaigns, social posts, and product visuals.\n- 2. Image editing - swap backgrounds, change object colors, or restyle portraits using simple text commands.\n- 3. Film & storytelling - create consistent characters across scenes for storyboards.\n- 4. Artistic transformations - convert photos into paintings, sketches, or cartoon styles.\n- 5. Marketing adjustments - edit text on signs, posters, or product labels.\n- 6. Rapid prototyping - iterate designs while preserving brand elements in seconds.\nPerfect for marketers, designers, and creators needing professional results without complex software.",
					},
					{
						question: "How can I write effective prompts for Flux Kontext?",
						answer: "Write clear, specific instructions to guide Flux Kontext accurately. For best results:\n- 1) Be precise about elements to modify (e.g., 'the red car on the left' instead of 'vehicle'),\n- 2) Use action verbs like 'change', 'swap', or 'transform',\n- 3) Add text in quotes when editing words (\"make the sign say 'OPEN'\"),\n- 4) Mention 'preserve composition' to keep layouts intact, \n- 5) Start simple then build complexity through quick, iterative edits.\nFlux Kontext delivers precise changes in 6-12 seconds while maintaining full image context.",
					},
					{
						question: "Where can I experience Flux Kontext image editing?",
						answer: "You can try Flux Kontext right here on Dreampik! We offer free credits so anyone can experience this powerful AI image generation tool at no cost. Simply submit your request through our platform - no payment required to start creating. Plus, Dreampik isn't just about Flux Kontext; explore several other advanced AI models all in one place for your creative projects.",
					},
				]}
			/>

			<FinalCTA
				ctaText="Try Flux Kontext Now"
				ctaTextDisplay={true}
				// ctaUrl="#"
				ctaUrl={`/ai-image-generator?model=${FLUX_1_KONTEXT_DEV.id}`}
				title="Edit Images in Seconds with Flux Kontext"
				description="Experience surgical AI editing that changes exactly what you describe - colors, text, or styles - while keeping everything else intact. No more full regenerations!"
			/>
		</main>
	);
}
