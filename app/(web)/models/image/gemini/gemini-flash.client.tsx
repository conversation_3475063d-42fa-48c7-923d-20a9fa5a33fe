"use client";

import { useState } from "react";
import { Textarea } from "@/components/ui/textarea";
import { SparklesIcon, LoaderCircle, Download, Icon, Loader2, ImagePlus, XCircle } from "lucide-react";
import { useSession } from "@/lib/auth-client";
import { useUserStore } from "@/store/useUserStore";
import { useSignInBoxOpenStore } from "@/store/useSignInBoxOpenStore";
import { usePlanBoxOpenStore } from "@/store/usePlanBoxOpenStore";
import { toast } from "sonner";
import { ofetch } from "ofetch";
import { AuthError, Credits402Error, handleError, IgnoreError } from "@/@types/error";
import { SubmitButton } from "@/components/ui/custom/submit-button";
import { Hint } from "@/components/ui/custom/hint";
import { coinsStack } from "@lucide/lab";
import { downloadImageFromBase64, downloadImageFromBase64WithWatermark, fileToBase64, imageUrlToBase64 } from "@/lib/file/utils-file";
import { Dropzone } from "@/components/ui/kibo-ui/dropzone";
import { uploadFile } from "@/lib/file/upload-file";
import { GEMINI_FLASH } from "@/config/image-models-config";
import { useSubmitTimer } from "@/hooks/use-submit-timer";
import { sendGTMEvent } from "@next/third-parties/google";
import { EVENT_GEN_IMAGE_PAGE } from "@/lib/track-events";

export default function GeminiFlashClient() {
	const { data: session } = useSession();
	const { user, refreshUser, hasPaid: userHasPaid } = useUserStore();
	const { setSignInBoxOpen } = useSignInBoxOpenStore();
	const { setPlanBoxOpen } = usePlanBoxOpenStore();

	const [prompt, setPrompt] = useState("");
	const [originImage, setOriginImage] = useState<
		{
			url: string;
			base64: string;
		}[]
	>([]);
	const [uploadingImage, setUploadingImage] = useState<boolean>(false);
	const [previewImageBase64, setPreviewImageBase64] = useState<string | null>(null);

	const handleLocalFileDrop = async (files: File[]) => {
		if (!session?.user) {
			setSignInBoxOpen(true);
			return;
		}

		setPreviewImageBase64(null);

		if (!files || files.length === 0) return;
		if (originImage.length >= 4) return;

		try {
			setUploadingImage(true);

			const { file_url } = await uploadFile(files[0]);
			const base64 = await fileToBase64(files[0]);
			setOriginImage([
				...originImage,
				{
					url: file_url,
					base64,
				},
			]);
		} catch (error: any) {
			console.error("Failed to upload image:", error.message);
			if (error instanceof AuthError) {
				setSignInBoxOpen(true);
				return;
			}
			toast.error(`Upload image failed: ${error.message}`);
		} finally {
			setUploadingImage(false);
		}
	};

	const [submitting, setSubmitting] = useState(false);
	const { seconds } = useSubmitTimer(submitting);
	const handleGenerateImage = async () => {
		if (!session) {
			setSignInBoxOpen(true);
			return;
		}
		if (!originImage) return;
		const promtpTrim = prompt.trim();
		if (!promtpTrim) return;

		sendGTMEvent({
			event: EVENT_GEN_IMAGE_PAGE,
			membership_level: user?.membershipLevel,
			model: GEMINI_FLASH.id,
		});

		try {
			setPreviewImageBase64(null);
			setSubmitting(true);
			const { status, message, resultUrl } = await ofetch("/api/v1/image/gemini-flash", {
				method: "POST",
				body: {
					prompt: promtpTrim,
					images: originImage.map((image) => image.url),
				},
			});
			handleError(status, message);
			refreshUser();
			if (resultUrl) {
				const base64 = await imageUrlToBase64(resultUrl);
				setPreviewImageBase64(base64);
			}

			toast.success("Generate success.");
		} catch (error: any) {
			console.error("Failed to generate image:", error.message);
			if (error instanceof AuthError) {
				setSignInBoxOpen(true);
				return;
			}

			if (error instanceof IgnoreError) {
				return;
			}
			if (error instanceof Credits402Error) {
				toast.warning("You do not have enough credits.", {
					action: {
						label: "Get more",
						onClick: () => setPlanBoxOpen(true),
					},
				});
				return;
			}
			toast.error("Generate failed.");
		} finally {
			setSubmitting(false);
		}
	};

	const [downloading, setDownloading] = useState(false);

	const removeImage = (indexToRemove: number) => {
		setOriginImage(originImage.filter((_, index) => index !== indexToRemove));
	};

	return (
		<div className="flex w-full flex-col items-center gap-6">
			<div className="bg-muted w-full rounded-lg px-4 pt-4 pb-2">
				<Textarea
					placeholder="Upload image, describe the edit, and click Generate...."
					value={prompt}
					maxLength={1000}
					onChange={(e) => setPrompt(e.target.value)}
					className="[&::-webkit-scrollbar-thumb]:bg-muted-foreground h-[126px] resize-none border-none bg-white shadow-none focus-visible:border-none focus-visible:ring-0 [&::-webkit-scrollbar]:my-2 [&::-webkit-scrollbar]:w-1.5 [&::-webkit-scrollbar-thumb]:rounded-full"
				/>

				<div className="mt-3 mb-1 flex flex-wrap items-end justify-between">
					<div className="grid grid-cols-2 items-center gap-1 sm:grid-cols-4">
						{originImage.map((image, index) => (
							<div key={index} className="group relative aspect-square w-[60px] shrink-0 rounded-md border">
								<img
									src={image.base64}
									alt="Model"
									className="h-full w-full rounded-md object-cover"
									onContextMenu={(e) => e.preventDefault()}
									onDragStart={(e) => e.preventDefault()}
								/>
								<button
									className="absolute -top-1.5 -right-1.5 z-10 cursor-pointer rounded-full bg-black/60 text-white hover:bg-black/70"
									onClick={(e) => {
										e.stopPropagation();
										removeImage(index);
									}}
								>
									<XCircle className="size-5" />
								</button>
							</div>
						))}
						{originImage.length < 4 && (
							<Dropzone
								multiple={false}
								maxFiles={1}
								onDrop={(files) => handleLocalFileDrop(files)}
								accept={{
									"image/jpeg": [".jpg", ".jpeg", ".png", ".webp"],
								}}
								className="aspect-square h-[60px] cursor-pointer border py-0 dark:bg-zinc-900/50 dark:hover:bg-zinc-800"
							>
								{uploadingImage ? <Loader2 className="animate-spin" /> : <ImagePlus />}
							</Dropzone>
						)}
					</div>

					<SubmitButton
						isSubmitting={submitting}
						onClick={handleGenerateImage}
						disabled={submitting || originImage.length === 0 || prompt.trim().length === 0}
						className="bg-foreground hover:bg-foreground/80 cursor-pointer"
					>
						<SparklesIcon className="h-4 w-4" />
						<span className="hidden md:block">Generate</span>{" "}
						{session && (
							<p className="flex flex-row items-center gap-0.5 text-xs font-normal [&_svg]:size-3">
								(<span>{GEMINI_FLASH.credits}</span> <Icon iconNode={coinsStack} />)
							</p>
						)}
					</SubmitButton>
				</div>
			</div>
			{(previewImageBase64 || submitting) && (
				<div className="group relative mx-auto flex aspect-square h-full w-full max-w-md items-center justify-center rounded-lg bg-zinc-800">
					{submitting && (
						<p className="flex w-full flex-col items-center text-center text-lg text-zinc-300">
							<LoaderCircle className="size-6 animate-spin" />
							<span className="text-sm tabular-nums">{seconds}s</span>
						</p>
					)}
					{previewImageBase64 && (
						<img
							src={previewImageBase64}
							alt="Generated image"
							className="h-full w-full rounded-lg object-contain"
							onContextMenu={(e) => e.preventDefault()}
							onDragStart={(e) => e.preventDefault()}
						/>
					)}
					<div className="absolute right-2 bottom-2 z-10 items-center gap-1">
						{previewImageBase64 && (
							<Hint label="Download image">
								<div className="relative">
									<SubmitButton
										isSubmitting={downloading}
										disabled={!previewImageBase64}
										size="icon"
										variant="secondary"
										className="cursor-pointer"
										onClick={async () => {
											try {
												setDownloading(true);
												if (userHasPaid) {
													await downloadImageFromBase64(previewImageBase64);
												} else {
													await downloadImageFromBase64WithWatermark(previewImageBase64);
												}
											} catch (error) {
												console.error("Failed to download image:", error);
											} finally {
												setDownloading(false);
											}
										}}
									>
										<Download />
									</SubmitButton>
								</div>
							</Hint>
						)}
					</div>
				</div>
			)}
		</div>
	);
}
