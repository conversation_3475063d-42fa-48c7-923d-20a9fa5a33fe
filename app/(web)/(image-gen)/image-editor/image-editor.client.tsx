"use client";

import { useState } from "react";
import { toast } from "sonner";
import { useSignInBoxOpenStore } from "@/store/useSignInBoxOpenStore";
import { ofetch } from "ofetch";
import { AuthError, IgnoreError, Credits402Error, handleError } from "@/@types/error";
import { SubmitButton } from "@/components/ui/custom/submit-button";
import { Download, LoaderCircle, CoinsIcon, SparklesIcon, PlusIcon, CrownIcon, LoaderIcon, CheckIcon } from "lucide-react";
import { Textarea } from "@/components/ui/textarea";
import { cn } from "@/lib/utils";
import { useSession } from "@/lib/auth-client";
import { useUserStore } from "@/store/useUserStore";
import { downloadImageFromBase64, downloadImageFromBase64WithWatermark, imageUrlToBase64 } from "@/lib/file/utils-file";
import { usePlanBoxOpenStore } from "@/store/usePlanBoxOpenStore";
import { IMAGE_SIZE_LIMIT_ } from "@/lib/constants";
import { useDropzone } from "react-dropzone";
import { Button, buttonVariants } from "@/components/ui/button";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { uploadFile } from "@/lib/file/upload-file";
import { sendGTMEvent } from "@next/third-parties/google";
import { EVENT_EDIT_IMAGE } from "@/lib/track-events";
import { FLUX_1_KONTEXT_DEV, FLUX_1_KONTEXT_MAX, FLUX_1_KONTEXT_PRO, HIDREAM_E1_1, ImageModel } from "@/config/image-models-config";
import { Badge } from "@/components/ui/badge";

const imageModels: ImageModel[] = [FLUX_1_KONTEXT_DEV, FLUX_1_KONTEXT_PRO, FLUX_1_KONTEXT_MAX, HIDREAM_E1_1];

export default function ImageEditor() {
	const { data: session } = useSession();
	const { user, refreshUser, hasPaid: userHasPaid } = useUserStore();
	const { setSignInBoxOpen } = useSignInBoxOpenStore();
	const { setPlanBoxOpen } = usePlanBoxOpenStore();

	const [model, setModel] = useState<ImageModel>(FLUX_1_KONTEXT_DEV);
	const [prompt, setPrompt] = useState<string>("");
	const [image, setImage] = useState<string | null>(null);

	const [allImages, setAllImages] = useState<string[]>([
		// "https://static.dreampik.art/source/202508/20250809-019890d3e34872d98d56e67bba358217.jpg",
		// "https://fast3d.io/images/blog/optimized/post-1-lg.webp",
	]);
	const {
		getRootProps,
		getInputProps,
		open: openDropzone,
	} = useDropzone({
		noClick: true,
		multiple: false,
		accept: {
			"image/jpeg": [],
			"image/png": [],
			"image/webp": [],
		},
		maxFiles: 1,
		onError: console.error,
		onDrop: (acceptedFiles, fileRejections, event) => {
			if (fileRejections.length > 0) {
				const message = fileRejections.at(0)?.errors.at(0)?.message;
				console.error?.(new Error(message));
				return;
			}

			handleLocalFileDrop(acceptedFiles);
		},
	});
	const [uploadingImage, setUploadingImage] = useState<boolean>(false);
	const handleLocalFileDrop = async (files: File[]) => {
		if (!session) {
			setSignInBoxOpen(true);
			return;
		}
		if (!files || files.length === 0) return;
		let file = files[0];

		if (file.size > IMAGE_SIZE_LIMIT_) {
			toast.warning("Image exceeds 4MB. Please upload a smaller one.");
			return;
		}

		try {
			setUploadingImage(true);
			const { file_url } = await uploadFile(file);
			setImage(file_url);
			setAllImages([file_url, ...allImages]);
		} catch (error: any) {
			console.error("Failed to upload image:", error.message);
			if (error instanceof AuthError) {
				setSignInBoxOpen(true);
				return;
			}
			toast.error(`Upload image failed: ${error.message}`);
		} finally {
			setUploadingImage(false);
		}
	};

	const [submitting, setSubmitting] = useState(false);
	const handleGenerateImage = async () => {
		if (submitting) return;
		if (!session) {
			setSignInBoxOpen(true);
			return;
		}
		const promtpTrim = prompt.trim();
		if (!promtpTrim) return;

		sendGTMEvent({
			event: EVENT_EDIT_IMAGE,
			membership_level: user?.membershipLevel,
			model: model.id,
		});

		try {
			setSubmitting(true);
			const { status, message, resultUrl } = await ofetch("/api/v1/image/edit-image", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: {
					model: model.id,
					prompt: promtpTrim,
					image: image,
				},
			});
			handleError(status, message);
			refreshUser();

			if (resultUrl) {
				setImage(resultUrl);
				setAllImages([resultUrl, ...allImages]);
			}

			toast.success("Generate success.");
		} catch (error: any) {
			console.error("Failed to generate image:", error);
			if (error instanceof AuthError) {
				setSignInBoxOpen(true);
				return;
			}
			if (error instanceof IgnoreError) {
				return;
			}
			if (error instanceof Credits402Error) {
				toast.warning("You do not have enough credits.", {
					action: {
						label: "Get more",
						onClick: () => setPlanBoxOpen(true),
					},
				});
				return;
			}
			toast.error(error.message || "Generate failed.");
		} finally {
			setSubmitting(false);
		}
	};

	const [downloading, setDownloading] = useState<boolean>(false);

	return (
		<div className="flex h-[calc(100vh-56px)] w-full flex-col">
			<div className="flex h-full w-full flex-col gap-4 px-4 pt-4 pb-4 md:flex-row" {...getRootProps()}>
				<div className="flex flex-1 flex-col items-center justify-between gap-4">
					<div className="group relative flex min-h-0 w-full flex-1 items-center justify-center">
						{image || uploadingImage ? (
							<div className="bg-muted relative flex aspect-square h-full max-h-[calc(100vh-326px)] max-w-full overflow-hidden rounded-lg md:max-h-[calc(100vh-242px)]">
								{/* <img src={`${OSS_URL_HOST}mkt/pages/ai-image-generator/demo.webp`} alt="AI Image Generator Demo" className="object-contain" /> */}
								<div
									className="aspect-square max-w-full bg-contain bg-center bg-no-repeat"
									// style={{ backgroundImage: `url(${OSS_URL_HOST}mkt/pages/ai-image-generator/demo.webp)` }}
									style={{ backgroundImage: `url(${image})` }}
									role="img"
								></div>

								{uploadingImage && (
									<div className="text-secondary bg-secondary-foreground/50 absolute inset-0 flex h-full w-full flex-col items-center justify-center">
										<LoaderIcon className="h-4 w-4 animate-spin" />
										<p className="text-sm">Uploading image...</p>
									</div>
								)}

								{image && (
									<div className="absolute right-2 bottom-2 z-10 items-center gap-1">
										<DropdownMenu>
											<DropdownMenuTrigger
												className={cn(buttonVariants({ variant: "default", size: "icon" }), "bg-foreground hover:bg-foreground/80")}
											>
												{downloading ? <LoaderCircle className="h-4 w-4 animate-spin" /> : <Download />}
											</DropdownMenuTrigger>
											<DropdownMenuContent className="border" align="start">
												{!userHasPaid && (
													<DropdownMenuItem
														className="flex cursor-pointer flex-row items-center gap-2 text-[13px] font-[350] [&>svg]:size-[14px]"
														onClick={async () => {
															try {
																setDownloading(true);
																const base64 = await imageUrlToBase64(image, { noCache: true });
																await downloadImageFromBase64WithWatermark(base64);
															} catch (error) {
																console.error("Failed to download image:", error);
															} finally {
																setDownloading(false);
															}
														}}
													>
														<Download className="text-zinc-800" /> Download with watermark
													</DropdownMenuItem>
												)}
												<DropdownMenuItem
													className="flex cursor-pointer flex-row items-center gap-2 text-[13px] font-[350] [&>svg]:size-[14px]"
													onClick={async () => {
														if (!userHasPaid) {
															setPlanBoxOpen(true);
															return;
														}
														try {
															setDownloading(true);
															const base64 = await imageUrlToBase64(image, { noCache: true });
															await downloadImageFromBase64(base64);
														} catch (error) {
															console.error("Failed to download image:", error);
														} finally {
															setDownloading(false);
														}
													}}
												>
													{userHasPaid ? (
														<>
															<Download className="text-zinc-800" /> Download
														</>
													) : (
														<>
															<CrownIcon className="text-yellow-500" /> Download without watermark
														</>
													)}
												</DropdownMenuItem>
											</DropdownMenuContent>
										</DropdownMenu>
									</div>
								)}
							</div>
						) : (
							<div className="">
								<Button
									disabled={uploadingImage}
									className="bg-foreground hover:bg-foreground relative h-auto w-full flex-row overflow-hidden rounded-2xl p-8"
									onClick={openDropzone}
								>
									<p className="flex h-4 w-4 items-center justify-center rounded-full bg-white">
										<PlusIcon className="text-secondary-foreground size-3" strokeWidth={3} />
									</p>
									Upload Image
								</Button>
							</div>
						)}
					</div>

					<div className="bg-muted w-full max-w-3xl shrink-0 rounded-xl px-4 py-2 backdrop-blur-3xl md:rounded-3xl">
						<Textarea
							placeholder="What do you want to change?"
							rows={5}
							maxLength={2000}
							className="h-16 resize-none border-none px-0 shadow-none placeholder:text-base focus-visible:text-base focus-visible:ring-0 md:text-base [&::-webkit-scrollbar]:hidden"
							value={prompt}
							onChange={(e) => setPrompt(e.target.value)}
						/>

						<div className="flex flex-row items-center justify-between gap-1 px-1">
							<DropdownMenu>
								<DropdownMenuTrigger asChild>
									<Button
										variant="secondary"
										size="sm"
										className="hover:ring-input justify-between gap-0.5 rounded-full bg-white text-xs font-medium shadow-none hover:bg-white hover:ring-2"
									>
										<img className="size-[16px]" src={model.logo} alt="" />
										<span className="text-[13px] font-normal">{model.name}</span>
									</Button>
								</DropdownMenuTrigger>
								<DropdownMenuContent className="w-auto rounded-xl">
									{imageModels.map((modelOption, index) => (
										<DropdownMenuItem
											key={index}
											className="flex cursor-pointer flex-row items-center justify-between gap-4 rounded-lg p-3"
											onClick={() => {
												setModel(modelOption);
											}}
										>
											<div className="flex flex-row items-center gap-2">
												<img className="size-[14px] text-white" src={modelOption.logo} alt="" />
												<span className="">{modelOption.name}</span>
												<Badge variant="secondary" className="bg-input/60 rounded-sm px-1.5 py-[1px] text-[10px]">
													<CoinsIcon className="size-2.5" />
													{modelOption.credits} credits
												</Badge>
											</div>
											{modelOption.id === model.id && (
												<p className="flex h-4 w-4 shrink-0 items-center justify-center rounded-full bg-black">
													<CheckIcon className="size-3 text-white" strokeWidth={3} />
												</p>
											)}
										</DropdownMenuItem>
									))}
								</DropdownMenuContent>
							</DropdownMenu>
							<SubmitButton
								className="bg-foreground hover:bg-foreground"
								isSubmitting={submitting}
								onClick={handleGenerateImage}
								{...{ disabled: submitting || !prompt.trim() || !image }}
							>
								<SparklesIcon />
								Generate
							</SubmitButton>
						</div>
					</div>
				</div>

				<div className="bg-muted flex shrink-0 flex-row gap-1 rounded-lg p-2 md:flex-col">
					<div className="">
						<Button
							variant="secondary"
							className="hover:ring-input h-[56px] w-[56px] flex-row overflow-hidden rounded-md bg-white shadow-none hover:bg-white hover:ring-2"
							onClick={openDropzone}
						>
							<PlusIcon />
							<input {...getInputProps()} />
						</Button>
					</div>
					<div
						className={cn(
							"flex flex-row gap-1 overflow-auto md:flex-col",
							"[&::-webkit-scrollbar-thumb]:bg-muted-foreground [&::-webkit-scrollbar]:h-1 md:[&::-webkit-scrollbar]:h-0 md:[&::-webkit-scrollbar]:w-0 [&::-webkit-scrollbar-thumb]:rounded-full",
						)}
					>
						{allImages.map((imageOption, index) => (
							<div
								key={index}
								className={cn("relative aspect-square h-[56px] w-[56px] overflow-hidden rounded-md", image === imageOption && "p-0.5")}
							>
								{/* <img
								src={image}
								alt="AI Image Generator Demo"
								className="aspect-square h-full w-full rounded object-cover"
								onContextMenu={(e) => e.preventDefault()}
								onDragStart={(e) => e.preventDefault()}
							/> */}
								<div
									className={cn(
										"aspect-square cursor-pointer rounded-sm bg-white bg-cover bg-center bg-no-repeat bg-origin-content",
										image === imageOption && "ring-2 ring-blue-500",
									)}
									style={{ backgroundImage: `url(${imageOption})` }}
									role="img"
									onClick={() => setImage(imageOption)}
								></div>
							</div>
						))}
					</div>
				</div>
			</div>
		</div>
	);
}
