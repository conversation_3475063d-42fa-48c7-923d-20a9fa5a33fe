import { OSS_URL_HOST, WEBNAME } from "@/lib/constants";
import type { Metadata } from "next";
import VideoGenClient from "@/components/app/video-gen.client";

export const metadata: Metadata = {
	title: `AI Image to Video Generator: Turn Images to Videos | ${WEBNAME}`,
	description: "",
	alternates: {
		canonical: "/image-to-video",
	},
	robots: {
		index: false,
		follow: false,
	},
};

export default async function Page() {
	return (
		<main className="min-h-screen pb-20">
			<section>
				<div className="relative pt-16 pb-20">
					<div className="mx-auto max-w-5xl px-6">
						<div className="mt-10 space-y-4 text-center sm:mx-auto">
							<h1 className="mx-auto max-w-4xl text-4xl font-semibold md:text-5xl">Image to Video Generator</h1>
							{/* <div className="text-muted-foreground mx-auto text-lg"></div> */}
						</div>

						<div className="mt-16">
							<VideoGenClient />
						</div>
					</div>
				</div>
			</section>
		</main>
	);
}
