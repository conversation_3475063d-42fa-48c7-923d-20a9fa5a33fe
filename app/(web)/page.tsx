import type { Metadata } from "next";
import { OSS_URL_HOST, WEBNA<PERSON> } from "@/lib/constants";
// import { Announcement, AnnouncementTag, AnnouncementTitle } from "@/components/ui/kibo-ui/announcement";
import ImageGenClient from "@/components/app/image-gen.client";
import { ImageMarqueeShow } from "@/components/landing/image-marquee-show";

export const metadata: Metadata = {
	title: `${WEBNAME} - AI Image Generator`,
	description: "Create stunning images instantly with our AI Image Generator. Transform text prompts into artwork using the most advanced AI models. ",
	alternates: {
		canonical: "/",
	},
};

export default async function Page() {
	return (
		<main className="min-h-screen">
			<section>
				<div className="relative pt-20 pb-24">
					<div className="mx-auto max-w-4xl px-6">
						<div className="mt-8 space-y-4 text-center sm:mx-auto">
							{/* <Link href="/flux/flux-kontext" className="cursor-pointer">
								<Announcement themed className="mb-4 bg-emerald-100 text-emerald-700">
									<AnnouncementTag className="group-[.announcement-themed]:bg-white/40">Latest update</AnnouncementTag>
									<AnnouncementTitle>
										FLUX.1 Kontext [dev] is now available!
										<ArrowUpRightIcon size={16} className="shrink-0 opacity-70" />
									</AnnouncementTitle>
								</Announcement>
							</Link> */}
							<h1 className="mx-auto max-w-4xl text-4xl font-semibold md:text-5xl">AI Image Generator</h1>
							<div className="text-muted-foreground mx-auto text-lg">
								Try the ultimate AI Image Generator. Access to the most advanced AI models and create stunning images from a prompt or an
								reference image.
							</div>
						</div>
					</div>
					<div className="container mt-8 max-w-5xl px-6">
						<ImageGenClient />
					</div>
				</div>
			</section>

			{/* <HomeMarqueeDemo /> */}

			<div className="py-20">
				<div className="container px-6 pb-8 text-center">
					<h2 className="text-[32px] font-semibold text-pretty">Get Inspired</h2>
					<p className="text-muted-foreground mx-auto mt-4 max-w-4xl text-pretty">Get inspired by what others are creating with Dreampik</p>
				</div>
				<ImageMarqueeShow
					images={[
						{ url: `${OSS_URL_HOST}mkt/pages/home/<USER>
						{ url: `${OSS_URL_HOST}mkt/pages/ai-image-generator/example-2.webp` },
						{ url: `${OSS_URL_HOST}mkt/pages/ai-image-generator/example-3.webp` },
						{ url: `${OSS_URL_HOST}mkt/pages/image-model/imagen/imagen4/example_5.webp` },
						{ url: `${OSS_URL_HOST}mkt/pages/home/<USER>
						{ url: `${OSS_URL_HOST}mkt/pages/home/<USER>
						{ url: `${OSS_URL_HOST}mkt/pages/home/<USER>
						{
							url: `${OSS_URL_HOST}mkt/pages/image-generators/ai-girl-generator/example_1.webp`,
							// prompt: "Young woman in an orange puffer jacket with sunglasses, gazing directly with a calm expression. Vibrant and stylish portrait.",
						},
						{ url: `${OSS_URL_HOST}mkt/pages/ai-image-generator/example-1.webp` },
						{ url: `${OSS_URL_HOST}mkt/pages/ai-image-generator/image-6.webp` },
						{ url: `${OSS_URL_HOST}mkt/pages/image-model/imagen/imagen4/example_2.webp` },
						{ url: `${OSS_URL_HOST}mkt/pages/image-model/imagen/imagen4/example_3.webp` },
						{ url: `${OSS_URL_HOST}mkt/pages/image-model/imagen/imagen4/example_4.webp` },
						{ url: `${OSS_URL_HOST}mkt/pages/image-model/imagen/imagen4/example_6.webp` },
						{ url: `${OSS_URL_HOST}mkt/pages/home/<USER>
						{ url: `${OSS_URL_HOST}mkt/pages/image-model/imagen/imagen4/example_8.webp` },
						{
							url: `${OSS_URL_HOST}mkt/pages/image-generators/ai-girl-generator/example_7.webp`,
							// prompt: "Stylish Asian woman in red sunglasses and shiny blue jacket exudes futuristic elegance. Bold red lipstick and gold earrings complete the look.",
						},
					]}
				/>
			</div>
		</main>
	);
}
