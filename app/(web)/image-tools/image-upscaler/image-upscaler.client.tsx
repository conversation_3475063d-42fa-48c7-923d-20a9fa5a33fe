"use client";

import { useState } from "react";
import { toast } from "sonner";
import { useSignInBoxOpenStore } from "@/store/useSignInBoxOpenStore";
import { ofetch } from "ofetch";
import { AuthError, IgnoreError, Credits402Error, handleError } from "@/@types/error";
import { SubmitButton } from "@/components/ui/custom/submit-button";
import { Download, CoinsIcon, SparklesIcon, LoaderIcon, PlusCircleIcon, XIcon } from "lucide-react";
import { cn } from "@/lib/utils";
import { useSession } from "@/lib/auth-client";
import { useUserStore } from "@/store/useUserStore";
import { downloadImageFromBase64, downloadImageFromBase64WithWatermark, fileToBase64, imageUrlToBase64 } from "@/lib/file/utils-file";
import { usePlanBoxOpenStore } from "@/store/usePlanBoxOpenStore";
import { Hint } from "@/components/ui/custom/hint";
import { uploadFile } from "@/lib/file/upload-file";
import { Comparison, ComparisonHandle, ComparisonItem } from "@/components/ui/kibo-ui/comparison";
import { IMAGE_SIZE_LIMIT_, OSS_URL_HOST } from "@/lib/constants";
import { sendGTMEvent } from "@next/third-parties/google";
import { EVENT_GEN_IMAGE_TOOL } from "@/lib/track-events";
import { useDropzone } from "react-dropzone";
import { buttonVariants } from "@/components/ui/button";

export default function ImageUpscaler() {
	const { data: session } = useSession();
	const { user, refreshUser, hasPaid: userHasPaid } = useUserStore();
	const { setSignInBoxOpen } = useSignInBoxOpenStore();
	const { setPlanBoxOpen } = usePlanBoxOpenStore();

	const [showDemo, setShowDemo] = useState(true);
	const [image, setImage] = useState<{
		base64: string;
		url: string;
	} | null>(null);
	const [previewImageBase64, setPreviewImageBase64] = useState<string | null>(null);
	// const [image, setImage] = useState<{
	// 	base64: string;
	// 	url: string;
	// } | null>({
	// 	base64: "https://static.youstylize.com/dev/image_result/202504/2025040701960f88c96272eb9f49f2e5e5ea7c0f.png",
	// 	url: "https://static.youstylize.com/dev/image_result/202504/2025040701960f88c96272eb9f49f2e5e5ea7c0f.png",
	// });
	// const [previewImageBase64, setPreviewImageBase64] = useState<string | null>(
	// 	"https://static.youstylize.com/dev/image_result/202504/2025040701960f88c96272eb9f49f2e5e5ea7c0f.png",
	// );

	const [uploadingImage, setUploadingImage] = useState<boolean>(false);
	const {
		getRootProps,
		getInputProps,
		open: openDropzone,
	} = useDropzone({
		noClick: true,
		multiple: false,
		accept: {
			"image/jpeg": [],
			"image/png": [],
			"image/webp": [],
		},
		maxFiles: 1,
		onError: console.error,
		onDrop: async (acceptedFiles, fileRejections, event) => {
			if (!session) {
				setSignInBoxOpen(true);
				return;
			}
			if (fileRejections.length > 0) {
				const message = fileRejections.at(0)?.errors.at(0)?.message;
				console.error?.(new Error(message));
				return;
			}

			const file = acceptedFiles[0];
			if (!file) return;
			if (file.size > IMAGE_SIZE_LIMIT_) {
				toast.warning("Image exceeds 4MB. Please upload a smaller one.");
				return;
			}

			try {
				setUploadingImage(true);
				const { file_url } = await uploadFile(file);
				// const file_url = "";
				const base64 = await fileToBase64(file);
				setImage({
					url: file_url,
					base64,
				});
			} catch (error: any) {
				console.error("Failed to upload image:", error.message);
				if (error instanceof AuthError) {
					setSignInBoxOpen(true);
					return;
				}
				toast.error(`Upload image failed: ${error.message}`);
			} finally {
				setUploadingImage(false);
			}
		},
	});

	const [submitting, setSubmitting] = useState(false);
	const handleGenerateImage = async () => {
		if (submitting) return;
		if (!session) {
			setSignInBoxOpen(true);
			return;
		}
		setShowDemo(false);

		sendGTMEvent({
			event: EVENT_GEN_IMAGE_TOOL,
			membership_level: user?.membershipLevel,
			tool: "upscale-image",
		});

		try {
			setPreviewImageBase64(null);
			setSubmitting(true);
			const { status, message, resultUrl } = await ofetch("/api/v1/image/tool/upscale-image", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: {
					image: image?.url,
				},
			});
			handleError(status, message);
			refreshUser();

			// const resultUrl = "/static/images/demo.jpg";

			if (resultUrl) {
				const base64 = await imageUrlToBase64(resultUrl as string, { noCache: true });
				setPreviewImageBase64(base64);
			}

			toast.success("Generate success.");
		} catch (error: any) {
			setPreviewImageBase64(null);
			console.error("Failed to generate image:", error);
			if (error instanceof AuthError) {
				setSignInBoxOpen(true);
				return;
			}
			if (error instanceof IgnoreError) {
				return;
			}
			if (error instanceof Credits402Error) {
				toast.warning("You do not have enough credits.", {
					action: {
						label: "Get more",
						onClick: () => setPlanBoxOpen(true),
					},
				});
				return;
			}
			toast.error(error.message ?? "Generate failed.");
		} finally {
			setSubmitting(false);
		}
	};

	const [downloadingImage, setDownloadingImage] = useState<boolean>(false);

	return (
		<div className="container grid w-full grid-cols-1 gap-4 px-6 py-4 sm:grid-cols-2">
			<div className="bg-muted w-full rounded-xl p-6">
				<div className="bg-input overflow-hidden rounded-lg" {...getRootProps()}>
					{image ? (
						<div className="group relative h-[260px] w-full md:h-[260px] lg:h-[300px]">
							<img
								src={image.base64}
								alt="Model"
								className="h-full w-full object-cover"
								onContextMenu={(e) => e.preventDefault()}
								onDragStart={(e) => e.preventDefault()}
							/>
							<button
								onClick={() => {
									setImage(null);
								}}
								className="bg-foreground/90 absolute top-2 right-2 flex h-9 w-9 cursor-pointer items-center justify-center rounded-full p-2 text-white"
							>
								<XIcon className="size-4" />
							</button>
						</div>
					) : (
						<div
							onClick={() => {
								if (uploadingImage) return;
								openDropzone();
							}}
							className="flex h-[200px] cursor-pointer items-center justify-center md:h-[260px] lg:h-[300px]"
						>
							{uploadingImage ? (
								<div className="text-muted-foreground flex flex-col items-center">
									<LoaderIcon className="animate-spin" />
									<p className="text-sm">Uploading image</p>
								</div>
							) : (
								<div className={cn(buttonVariants({ size: "lg" }), "bg-action hover:bg-action/80 h-14 rounded-xl")}>
									<PlusCircleIcon />
									<p>Upload image</p>
								</div>
							)}
							<input {...getInputProps()} />
						</div>
					)}
				</div>

				<div className="mt-4">
					<SubmitButton
						size="lg"
						className="bg-foreground hover:bg-foreground/80 w-full rounded-lg"
						isSubmitting={submitting}
						onClick={handleGenerateImage}
						{...{ disabled: submitting || !image }}
					>
						<SparklesIcon />
						Upscale
						{session && (
							<p className="text-secondary flex flex-row items-center gap-0.5 px-2 py-0.5 text-[11px]">
								(<CoinsIcon className="size-3" />
								2)
							</p>
						)}
					</SubmitButton>
				</div>
			</div>

			<div className="bg-muted flex items-start justify-center rounded-xl p-6">
				<div className="h-full w-full">
					{showDemo ? (
						<div className="mx-auto flex h-full max-w-2xl flex-col justify-center space-y-2">
							<div className="mx-auto flex w-full max-w-3xl overflow-hidden rounded-lg">
								<Comparison className="aspect-[3/2] max-w-3xl">
									<ComparisonItem position="right" className="">
										<div className="relative">
											<img
												src={`${OSS_URL_HOST}mkt/pages/image-tools/image-upscaler/image-upscaler-sample-before.avif`}
												alt="Image Upscaler Sample Before"
												className="h-full w-full object-contain"
												onContextMenu={(e) => e.preventDefault()}
												onDragStart={(e) => e.preventDefault()}
											/>
											<div
												className="bg-secondary-foreground/60 absolute top-2 left-2 rounded-sm px-2 py-1 text-xs text-white backdrop-blur-xs after:content-(--content)"
												style={{ "--content": "'Before'" } as React.CSSProperties}
											></div>
										</div>
									</ComparisonItem>
									<ComparisonItem position="left" className="">
										<div className="relative">
											<img
												src={`${OSS_URL_HOST}mkt/pages/image-tools/image-upscaler/image-upscaler-sample-after.avif`}
												alt="Image Upscaler Sample After"
												className="h-full w-full object-contain"
												onContextMenu={(e) => e.preventDefault()}
												onDragStart={(e) => e.preventDefault()}
											/>
											<div
												className="bg-secondary-foreground/60 absolute top-2 right-2 rounded-sm px-2 py-1 text-xs text-white backdrop-blur-xs after:content-(--content)"
												style={{ "--content": "'After'" } as React.CSSProperties}
											></div>
										</div>
									</ComparisonItem>
									<ComparisonHandle />
								</Comparison>
							</div>
						</div>
					) : (
						<div className="mx-auto flex h-full max-w-3xl flex-col justify-center space-y-4">
							<div
								className={cn(
									"group relative mx-auto flex w-full max-w-3xl justify-center overflow-hidden rounded-lg",
									(previewImageBase64 || submitting) && "aspect-[3/2]",
								)}
							>
								{previewImageBase64 && image && (
									<>
										<Comparison className="aspect-[3/2] max-w-3xl">
											<ComparisonItem position="right" className="">
												<div className="relative">
													<div
														className="aspect-[3/2] h-full w-full bg-contain bg-center bg-no-repeat"
														style={{ backgroundImage: `url(${image.base64})` }}
														role="img"
														aria-label="Original image"
													></div>
													<div
														className="bg-secondary-foreground/60 absolute top-2 left-2 rounded-sm px-2 py-1 text-xs text-white backdrop-blur-xs after:content-(--content)"
														style={{ "--content": "'Before'" } as React.CSSProperties}
													></div>
												</div>
											</ComparisonItem>
											<ComparisonItem position="left" className="">
												<div className="relative">
													<div
														className="aspect-[3/2] h-full w-full bg-contain bg-center bg-no-repeat"
														style={{ backgroundImage: `url(${previewImageBase64})` }}
														role="img"
														aria-label="Upscaled image"
													></div>
													<div
														className="bg-secondary-foreground/60 absolute top-2 right-2 rounded-sm px-2 py-1 text-xs text-white backdrop-blur-xs after:content-(--content)"
														style={{ "--content": "'After'" } as React.CSSProperties}
													></div>
												</div>
											</ComparisonItem>
											<ComparisonHandle />
										</Comparison>
										<div className="absolute right-2 bottom-2 z-10 items-center gap-1">
											{previewImageBase64 && (
												<Hint label="Download image">
													<div className="relative">
														<SubmitButton
															isSubmitting={downloadingImage}
															disabled={!previewImageBase64}
															size="icon"
															className="bg-foreground hover:bg-foreground/80"
															onClick={async () => {
																try {
																	setDownloadingImage(true);
																	if (userHasPaid) {
																		await downloadImageFromBase64(previewImageBase64);
																	} else {
																		await downloadImageFromBase64WithWatermark(previewImageBase64);
																	}
																} catch (error) {
																	console.error("Failed to download image:", error);
																} finally {
																	setDownloadingImage(false);
																}
															}}
														>
															<Download />
														</SubmitButton>
													</div>
												</Hint>
											)}
										</div>
									</>
								)}
							</div>
						</div>
					)}
				</div>
			</div>
		</div>
	);
}
