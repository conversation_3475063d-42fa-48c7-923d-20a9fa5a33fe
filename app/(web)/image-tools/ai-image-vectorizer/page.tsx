import { OSS_URL_HOST, WEBNAME } from "@/lib/constants";
import type { Metadata } from "next";
import { VectorizeImageClient } from "./vectorize-image.client";
import FinalCTA from "@/components/landing/final-cta";
import FAQsComponent from "@/components/landing/faqs";
import { HowToUse } from "@/components/landing/how-to-use";
import FeaturesComponent from "@/components/landing/features";
import BreadcrumbPath from "@/components/landing/breadcrumb-path";

export const metadata: Metadata = {
	title: `AI Vectorize Image: Convert JPG & PNG to SVG | ${WEBNAME}`,
	description:
		"Easily vectorize your images with AI. Convert PNG or JPG to scalable SVG files for clear lines and perfect quality. No more blurry images—get started today!",
	alternates: {
		canonical: "/image-tools/ai-image-vectorizer",
	},
};

export default async function Page() {
	return (
		<main className="min-h-screen">
			<div className="px-6 pt-3">
				<BreadcrumbPath
					paths={[
						{ title: "Home", href: "/" },
						{ title: "Image Tools", href: "/image-tools" },
					]}
					current={"AI Image Vectorizer"}
				/>
			</div>

			<div className="relative pt-8 pb-12">
				<div className="mx-auto max-w-4xl px-6">
					<div className="mt-8 space-y-6 text-center sm:mx-auto">
						<h1 className="mx-auto max-w-4xl text-4xl font-semibold md:text-5xl">AI Image Vectorizer</h1>
						<div className="text-muted-foreground mx-auto mt-4 text-balance">
							Convert your JPGs and PNGs into full-color, scalable SVGs with smooth curves and clean lines with our AI image vectorizer.
						</div>
					</div>

					{/* <div className="mt-8 flex flex-col items-center justify-center gap-2 md:flex-row">
						<Link
							href="#"
							className={cn(
								buttonVariants({ size: "lg" }),
								`bg-action hover:bg-action/80 h-12 rounded-full text-base text-nowrap after:content-(--content)`,
							)}
							// style={{ "--content": "'Create Images'" } as React.CSSProperties}
						>
							Vectorize Image Now
						</Link>
					</div> */}

					{/* <div className="mx-auto mt-12 flex w-full max-w-3xl overflow-hidden rounded-xl">
						<Comparison className="aspect-[3/2] max-w-4xl">
							<ComparisonItem position="right" className="">
								<div className="relative">
									<img
										src={`${OSS_URL_HOST}mkt/pages/image-tools/image-vectorizer/vectorize-sample-before.webp`}
										alt="Image Vectorizer Sample Before"
										className="aspect-[3/2] h-full w-full object-contain"
										loading="lazy"
									/>
									<div
										className="bg-secondary-foreground/60 absolute top-2 left-2 rounded-sm px-2 py-1 text-xs text-white backdrop-blur-xs after:content-(--content)"
										style={{ "--content": "'Image'" } as React.CSSProperties}
									></div>
								</div>
							</ComparisonItem>
							<ComparisonItem position="left" className="">
								<div className="relative">
									<img
										src={`${OSS_URL_HOST}mkt/pages/image-tools/image-vectorizer/vectorize-sample-after.svg`}
										alt="Image Vectorizer Sample After"
										className="aspect-[3/2] h-full w-full rounded-lg object-contain"
										loading="lazy"
									/>
									<div
										className="bg-secondary-foreground/60 absolute top-2 right-2 rounded-sm px-2 py-1 text-xs text-white backdrop-blur-xs after:content-(--content)"
										style={{ "--content": "'SVG'" } as React.CSSProperties}
									></div>
								</div>
							</ComparisonItem>
							<ComparisonHandle />
						</Comparison>
					</div> */}
				</div>
			</div>

			<div className="pb-20">
				<VectorizeImageClient />
			</div>

			<FeaturesComponent
				ctaText="Vectorize Image Now"
				ctaUrl="#"
				badge="SVG"
				features={[
					{
						title: "Perfect Clarity at Any Size",
						description:
							"Never worry about blurry or pixelated images again. Our AI-powered image vectorizer transforms your JPG, PNG, or any pixel-based picture into crisp, clean SVG vectors. Scale your logos, illustrations, and graphics infinitely—always sharp for print, web, and everywhere your brand shines.",
						image: `${OSS_URL_HOST}mkt/pages/image-tools/image-vectorizer/feature-1.svg`,
						imageAlt: "Example of a clear, scalable SVG image",
					},
					{
						title: "Lightning-Fast, Effortless Conversion",
						description:
							"Save hours of tedious manual tracing. With just one click, our AI does the heavy lifting to vectorize your image in seconds—no design skills needed. Upload your photo, and instantly get a stunning vector that’s ready for editing or use. No stress. No steep learning curve. Just beautiful results.",
						image: `${OSS_URL_HOST}mkt/pages/image-tools/image-vectorizer/feature-2.svg`,
						imageAlt: "Fast and easy image vectorization process",
					},
					{
						title: "Preserve Every Detail—Automatically",
						description:
							"Don’t let important details get lost in translation. Our intelligent vectorizer detects even subtle colors, gradients, and curves, ensuring your converted SVG looks just like the original image—only better. You keep all the detail, with zero compromise on quality.",
						image: `${OSS_URL_HOST}mkt/pages/image-tools/image-vectorizer/feature-3.svg`,
						imageAlt: "AI preserving intricate details during vector conversion",
					},
					{
						title: "Freedom to Customize and Create",
						description:
							"Once your picture is vectorized, you’re in control. Edit colors, shapes, and lines freely—without any degradation. From logo design to custom illustrations, our AI gives you a vector foundation that’s built for creativity and endless possibilities, empowering you to bring your ideas to life.",
						image: `${OSS_URL_HOST}mkt/pages/image-tools/image-vectorizer/feature-4.svg`,
						imageAlt: "Customizable SVG vector image for creative freedom",
					},
				]}
			/>

			<HowToUse
				title="How to Convert PNG/JPG to SVG"
				steps={[
					{
						title: "1. Upload Your Image",
						description:
							"Choose a JPG or PNG file and upload it to our image vectorizer. Drag and drop, or click to select your image—it’s simple and secure.",
					},
					{
						title: "2. AI Vectorize",
						description:
							"Our AI instantly converts your picture to vector. It creates a clean, scalable SVG with sharp colors and smooth lines—no manual work needed.",
					},
					{
						title: "3. Download Your SVG",
						description:
							"Download your new SVG file in one click. Enjoy crisp, high-quality vector images you can use for any project, big or small.",
					},
				]}
			/>

			<FAQsComponent
				title="AI Image Vectorizer Related FAQs"
				faqs={[
					{
						question: "What is AI image vectorizer?",
						answer: "AI image vectorizer is a tool that uses artificial intelligence to convert a pixel-based image, like JPG or PNG, into a smooth, scalable vector format, like SVG. Unlike regular images that get blurry when stretched, vectors stay sharp and clear no matter how big or small you make them—perfect for logos, illustrations, and print designs.",
					},
					{
						question: "Why should I vectorize my image?",
						answer: "Vectorizing your image makes it endlessly scalable without losing quality, so you’ll never have to worry about blurry or pixelated graphics again. Vector files are also usually smaller and load faster, and they’re much easier to edit for color, size, or shape. That’s why pros use vector images for branding, printing, and web graphics.",
					},
					{
						question: "What’s the main difference between a vector image and a pixel (raster) image?",
						answer: "Pixel or raster images (like JPG and PNG) are made up of thousands of tiny squares. When you zoom in or enlarge them, they can get blocky or fuzzy. Vector images (like SVGs) are built from crisp lines, curves, and shapes defined by math—not pixels—so they always look sharp, no matter the size. Our AI image vectorizer makes this transformation simple and fast.",
					},
					{
						question: "Which image formats can I convert to vector with your tool?",
						answer: "You can upload standard JPG and PNG files to our AI-powered vectorizer and turn them into high-quality SVG vector images in just seconds. We’re working on supporting even more formats soon!",
					},
					{
						question: "What makes your AI image vectorizer special?",
						answer: "Dreampik uses advanced AI to deliver unbeatable vectorization quality. Our tool intelligently detects edges and colors in your JPG or PNG file, producing SVG images with smooth curves and clean lines. You get results that look professional, detailed, and true to your original image—without any pixelation.",
					},
					{
						question: "How long does it take to vectorize an image with your AI tool?",
						answer: "It takes just a few seconds! Simply upload your image, and our AI image vectorizer works its magic almost instantly. No special skills or downloads required—fast, simple, and effective.",
					},
					{
						question: "What types of images work best with your AI image vectorizer?",
						answer: "Our AI image vectorizer is ideal for logos, brand designs, illustrations, icons, and art with clear lines and shapes. It’s also great for images you need for high-quality print, like T-shirt designs, posters, or flyers. If you want your graphics to always look crisp and professional, this tool is for you.",
					},
					{
						question: "Will the vectorized image keep the quality of my original?",
						answer: "Yes! Our AI has been specially trained to keep your image’s fine details, colors, and edges as precise as possible during the vectorization process. While extremely complex photos may not be 100% perfect, logos and illustrations come out clean and sharp—just the way you want them.",
					},
					{
						question: "Can I try the AI image vectorizer for free?",
						answer: "Absolutely! Dreampik offers free trial credits so you can test our AI image vectorizer with no risk. Try it out, see your results, and upgrade anytime if you need more conversions.",
					},
					{
						question: "What is Dreampik?",
						answer: "Dreampik is your all-in-one AI platform for creating and editing images and videos. We offer the latest image and video AI models—as well as handy tools like our AI image vectorizer—to help you create, convert, and enhance your digital content faster and easier than ever before.",
					},
				]}
			/>

			<FinalCTA
				title="Instantly Vectorize Your Image with AI"
				description="Transform your blurry, pixelated PNG or JPG into a crisp, scalable SVG in seconds—no design skills needed. Experience the freedom and confidence of perfect graphics that look flawless at any size. Start turning your pictures into sharp, professional vectors today."
				ctaText="Convert to SVG vector images"
				ctaTextDisplay={true}
				ctaUrl="#"
				ctaClassName="rounded-full"
			/>
		</main>
	);
}
