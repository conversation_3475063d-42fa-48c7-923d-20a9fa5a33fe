import { getSessionUserId } from "@/server/auth/auth-session";
import { NextResponse } from "next/server";
import { getDB } from "@/server/db/db-client.server";
import { getUUIDString } from "@/lib/utils";
import { OSS_URL_HOST, WEBNAME } from "@/lib/constants";
import { MediaHead, mediaHeadSchema, mediaItemSchema } from "@/server/db/schema.server";
import { MediaHeadToolType, MediaResultStatus } from "@/@types/media/media-type";
import { checkUserCredit, updateUserCredit } from "@/server/utils-credits.server";
import {
	getImageModel,
	FLUX_1_1_PRO,
	FLUX_1_1_PRO_ULTRA,
	FLUX_1_KONTEXT_PRO,
	FLUX_1_KONTEXT_MAX,
	IDEOGRAM_3_TURBO,
	IMAGEN_4_PREVIEW,
	RECRAFT_3,
	IMAGEN_4_PREVIEW_FAST,
	IMAGEN_4_PREVIEW_ULTRA,
	SEEDREAM_3,
	FLUX_1_KONTEXT_DEV,
	FLUX_1_SCHNELL,
	FLUX_1_DEV,
	IDEOGRAM_3_BALANCED,
	HIDREAM_I1_FULL,
	HIDREAM_E1_1,
	FLUX_KREA,
	QWEN_IMAGE,
	DREAMINA_3_1,
	HIDREAM_I1_DEV,
} from "@/config/image-models-config";
import { saveToR2 } from "@/server/r2.server";
import { handleApiError } from "@/@types/error-api";
import { genGoogleImagen4FromFal } from "@/server/ai/imagen.server";
import {
	genFluxDevFromFal,
	genFluxKontextDevFromFal,
	genFluxKreaFromFal,
	genFluxProFromFal,
	genFluxProKontextFromFal,
	genFluxSchnellFromFal,
} from "@/server/ai/flux.server";
import { genIdeogramFromFal } from "@/server/ai/ideogram.server";
import { genRecraft3FromFal } from "@/server/ai/recraft.server";
import { genSeedream3FromFal } from "@/server/ai/seedream.server";
import { mixpanelTrackEvent } from "@/server/mixpanel.server";
import { EVENT_GEN_IMAGE_TO_IMAGE, EVENT_GEN_TEXT_TO_IMAGE } from "@/lib/track-events";
import { genHidreamE1FromFal, genHidreamI1FromFal, genHidreamI1FullFromFal } from "@/server/ai/hidream.server";
import { genQwenImageFromFal } from "@/server/ai/qwen-image.server";
import { genDreamina3_1FromFal } from "@/server/ai/dreamina.server";

type Params = {
	model: string;
	prompt: string;
	size: {
		ratio: string;
		width: number;
		height: number;
	};
	numImages: number;
	image?: string;
	modelStyle?: string;
	visibility: string;
};

export async function POST(req: Request) {
	const cfIpCountryCode = req.headers.get("cf-ipcountry");
	// const cfIp = req.headers.get("cf-connecting-ip");
	const params: Params = await req.json();
	if (process.env.NODE_ENV === "development") {
		console.log("params: ", params);
	}
	if (!params.model || !params.prompt || !params.size || !params.numImages) {
		return NextResponse.json({ status: 400, message: "Parameters is missing." });
	}

	try {
		const userId = await getSessionUserId();

		const imageModel = getImageModel(params.model);
		const needCredits = imageModel.credits * params.numImages;
		const { creditConsumes, visibility } = await checkUserCredit(userId, {
			needCredits: needCredits,
		});

		if (process.env.NODE_ENV === "development") {
			console.log("params: ", params);
			console.log("creditConsumes: ", creditConsumes);
		}

		// track mixpanel event
		mixpanelTrackEvent(params.image ? EVENT_GEN_IMAGE_TO_IMAGE : EVENT_GEN_TEXT_TO_IMAGE, userId, {
			mp_country_code: cfIpCountryCode,
			model: imageModel.id,
			free: visibility,
		});

		const imageResultId = getUUIDString();
		let resultUrls: string[];
		switch (imageModel.model) {
			// case FLUX_1_FAST.model:
			// case FLUX_1_DEV.model:
			// 	resultUrls = await genFluxFromWavespeed(imageModel.model, params.prompt, params.numImages, params.size, params.image);
			// 	break;
			case FLUX_1_SCHNELL.model:
				resultUrls = await genFluxSchnellFromFal(imageModel.model, params.prompt, params.numImages, params.size);
				break;
			case FLUX_KREA.model:
				resultUrls = await genFluxKreaFromFal(imageModel.model, params.prompt, params.numImages, params.size, params.image);
				break;
			case FLUX_1_DEV.model:
				resultUrls = await genFluxDevFromFal(imageModel.model, params.prompt, params.numImages, params.size, params.image);
				break;
			case FLUX_1_1_PRO.model:
			case FLUX_1_1_PRO_ULTRA.model:
				resultUrls = await genFluxProFromFal(imageModel.model, params.prompt, params.numImages, params.size, params.image);
				break;
			case FLUX_1_KONTEXT_DEV.model:
				resultUrls = await genFluxKontextDevFromFal(params.prompt, params.numImages, params.image!);
				break;
			case FLUX_1_KONTEXT_PRO.model:
			case FLUX_1_KONTEXT_MAX.model:
				resultUrls = await genFluxProKontextFromFal(imageModel.model, params.prompt, params.numImages, undefined, params.image);
				break;
			case IMAGEN_4_PREVIEW.model:
			case IMAGEN_4_PREVIEW_FAST.model:
			case IMAGEN_4_PREVIEW_ULTRA.model:
				resultUrls = await genGoogleImagen4FromFal(imageModel.model, params.prompt, params.numImages, params.size.ratio);
				break;
			case IDEOGRAM_3_TURBO.model:
			case IDEOGRAM_3_BALANCED.model:
				resultUrls = await genIdeogramFromFal(imageModel.model, params.prompt, params.numImages, params.size, params.image, params.modelStyle);
				break;
			case RECRAFT_3.model:
				resultUrls = await genRecraft3FromFal(imageModel.model, params.prompt, params.size, params.image, params.modelStyle);
				break;
			case SEEDREAM_3.model:
				resultUrls = await genSeedream3FromFal(imageModel.model, params.prompt, params.numImages, params.size);
				break;
			case DREAMINA_3_1.model:
				resultUrls = await genDreamina3_1FromFal(imageModel.model, params.prompt, params.numImages, params.size);
				break;
			case QWEN_IMAGE.model:
				resultUrls = await genQwenImageFromFal(imageModel.model, params.prompt, params.numImages, params.size);
				break;
			case HIDREAM_I1_DEV.model:
				resultUrls = await genHidreamI1FromFal(imageModel.model, params.prompt, params.numImages, params.size);
				break;
			case HIDREAM_I1_FULL.model:
				resultUrls = await genHidreamI1FullFromFal(imageModel.model, params.prompt, params.numImages, params.size, params.image);
				break;
			case HIDREAM_E1_1.model:
				resultUrls = await genHidreamE1FromFal(imageModel.model, params.prompt, params.numImages, params.image);
				break;
			default:
				throw new Error(`Unsupported model: ${imageModel.model}`);
		}

		if (process.env.NODE_ENV === "development") {
			console.log("resultUrls: ", resultUrls);
		}

		// save to r2
		const imagePaths = await Promise.all(resultUrls.map((url) => saveToR2(url)));

		// save to db
		const db = getDB();
		await db.transaction(async (tx) => {
			const [media]: MediaHead[] = await tx
				.insert(mediaHeadSchema)
				.values({
					uid: imageResultId,
					userId: userId,
					model: imageModel.id,
					modelName: imageModel.name,
					tool: params.image ? MediaHeadToolType.ImageToImage : MediaHeadToolType.TextToImage,
					prompt: params.prompt,
					status: MediaResultStatus.Completed,
					creditsSources: JSON.stringify(creditConsumes),
				})
				.returning();
			await tx.insert(mediaItemSchema).values(
				resultUrls.map((resultUrl, index) => ({
					uid: getUUIDString(),
					userId: userId,
					mediaHeadUid: media.uid,
					visibility: params.visibility === "public",
					mediaPaths: imagePaths[index],
					mediaOriginUrls: resultUrl,
				})),
			);
		});

		// //更新用户token
		await updateUserCredit(userId, creditConsumes, {
			remark: `Image task id: ${imageResultId}.`,
		});

		return NextResponse.json({ message: "Success", resultUrls: imagePaths.map((path) => `${OSS_URL_HOST}${path}`) });
	} catch (error: any) {
		return handleApiError(error, `${WEBNAME} - /api/v1/image/generate-image`);
	}
}
