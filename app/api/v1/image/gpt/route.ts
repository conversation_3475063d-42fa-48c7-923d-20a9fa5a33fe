import { getSessionUserId } from "@/server/auth/auth-session";
import { NextResponse } from "next/server";
import { getDB } from "@/server/db/db-client.server";
import { WEBNAME } from "@/lib/constants";
import { mediaTaskSchema } from "@/server/db/schema.server";
import { MediaHeadToolType, MediaHeadType, MediaResultStatus } from "@/@types/media/media-type";
import { checkUserCredit, updateUserCredit } from "@/server/utils-credits.server";
import { handleApiError } from "@/@types/error-api";
import { fal } from "@fal-ai/client";
import { CALLBACK_URL_FAL } from "@/lib/constants";
import { FLUX_1_KONTEXT_PRO } from "@/config/image-models-config";

type Params = {
	prompt: string;
	size: {
		ratio: string;
		width: number;
		height: number;
	};
	numImages: number;
	image?: string;
};

export async function POST(req: Request) {
	// const cfIpCountryCode = req.headers.get("cf-ipcountry");
	const cfIp = req.headers.get("cf-connecting-ip");
	const params: Params = await req.json();
	if (!params.prompt || !params.size || !params.numImages) {
		return NextResponse.json({ status: 400, message: "Parameters is missing." });
	}

	try {
		const userId = await getSessionUserId();

		const imageModel = FLUX_1_KONTEXT_PRO;
		if (!imageModel) {
			throw new Error("Image model is not found.");
		}
		const needCredits = imageModel.credits * params.numImages;
		const { creditConsumes, visibility } = await checkUserCredit(userId, {
			needCredits: needCredits,
		});

		let payload: any = {
			prompt: params.prompt,
			num_images: params.numImages,
		};
		if (params.image) {
			payload.image_url = params.image;
		}
		if (process.env.NODE_ENV === "development") {
			console.log("params: ", params);
			console.log("creditConsumes: ", creditConsumes);
			console.log("fal.ai flux1 kontext payload: ", payload);
		}

		fal.config({
			credentials: process.env.FAL_API_KEY,
		});
		const { request_id } = await fal.queue.submit(`fal-ai/flux-pro/kontext${params.image ? "" : "/text-to-image"}`, {
			input: payload,
			webhookUrl: CALLBACK_URL_FAL,
		});

		// save to db
		const db = getDB();
		await db.insert(mediaTaskSchema).values({
			userId: userId,
			type: MediaHeadType.Image,
			model: imageModel.id,
			modelName: imageModel.name,
			tool: params.image ? MediaHeadToolType.ImageToImage : MediaHeadToolType.TextToImage,
			prompt: params.prompt,
			aspectRatio: params.size.ratio,
			requestId: request_id,
			status: MediaResultStatus.InProgress,
			visibility: visibility,
			creditsSources: JSON.stringify(creditConsumes),
			ip: cfIp,
		});
		// //更新用户token
		await updateUserCredit(userId, creditConsumes, {
			remark: `Image task request id: ${request_id}.`,
		});

		return NextResponse.json({ message: "Success", task_status: MediaResultStatus.InProgress, request_id: request_id });
	} catch (error: any) {
		return handleApiError(error, `${WEBNAME} - /api/v1/image/gpt`);
	}
}
