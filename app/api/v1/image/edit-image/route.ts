import { getSessionUserId } from "@/server/auth/auth-session";
import { NextResponse } from "next/server";
import { getDB } from "@/server/db/db-client.server";
import { getUUIDString } from "@/lib/utils";
import { OSS_URL_HOST, WEBNAME } from "@/lib/constants";
import { MediaHead, mediaHeadSchema, mediaItemSchema } from "@/server/db/schema.server";
import { MediaHeadToolType, MediaResultStatus } from "@/@types/media/media-type";
import { checkUserCredit, updateUserCredit } from "@/server/utils-credits.server";
import { FLUX_1_KONTEXT_PRO, FLUX_1_KONTEXT_MAX, FLUX_1_KONTEXT_DEV, HIDREAM_E1_1, ImageModel } from "@/config/image-models-config";
import { saveToR2 } from "@/server/r2.server";
import { handleApiError } from "@/@types/error-api";
import { genFluxKontextDevFromFal, genFluxProKontextFromFal } from "@/server/ai/flux.server";
import { mixpanelTrackEvent } from "@/server/mixpanel.server";
import { EVENT_EDIT_IMAGE } from "@/lib/track-events";
import { genHidreamE1FromFal } from "@/server/ai/hidream.server";

type Params = {
	model: string;
	prompt: string;
	image: string;
};

const imageEditorModels: ImageModel[] = [FLUX_1_KONTEXT_DEV, FLUX_1_KONTEXT_PRO, FLUX_1_KONTEXT_MAX, HIDREAM_E1_1];

export async function POST(req: Request) {
	const cfIpCountryCode = req.headers.get("cf-ipcountry");
	// const cfIp = req.headers.get("cf-connecting-ip");
	const params: Params = await req.json();
	if (!params.model || !params.prompt || !params.image) {
		return NextResponse.json({ status: 400, message: "Parameters is missing." });
	}

	try {
		const userId = await getSessionUserId();

		const imageModel = imageEditorModels.find((imageModel) => imageModel.id === params.model);
		if (process.env.NODE_ENV === "development") {
			console.log("imageModel: ", imageModel);
		}
		if (!imageModel) {
			throw new Error("Image model is not found.");
		}

		const needCredits = imageModel.credits;
		const { creditConsumes, visibility } = await checkUserCredit(userId, {
			needCredits: needCredits,
		});

		if (process.env.NODE_ENV === "development") {
			console.log("params: ", params);
			console.log("creditConsumes: ", creditConsumes);
		}

		// track mixpanel event
		mixpanelTrackEvent(EVENT_EDIT_IMAGE, userId, {
			mp_country_code: cfIpCountryCode,
			model: imageModel.id,
			free: visibility,
		});

		const imageResultId = getUUIDString();
		let resultUrls: string[];
		switch (imageModel.model) {
			case FLUX_1_KONTEXT_DEV.model:
				resultUrls = await genFluxKontextDevFromFal(params.prompt, 1, params.image);
				break;
			case FLUX_1_KONTEXT_PRO.model:
			case FLUX_1_KONTEXT_MAX.model:
				resultUrls = await genFluxProKontextFromFal(imageModel.model, params.prompt, 1, undefined, params.image);
				break;
			case HIDREAM_E1_1.model:
				resultUrls = await genHidreamE1FromFal(HIDREAM_E1_1.model, params.prompt, 1, params.image);
				break;
			default:
				throw new Error(`Unsupported model: ${imageModel.model}`);
		}
		const resultUrl = resultUrls[0];

		// save to r2
		const imagePath = await saveToR2(resultUrl);

		// save to db
		const db = getDB();
		await db.transaction(async (tx) => {
			const [media]: MediaHead[] = await tx
				.insert(mediaHeadSchema)
				.values({
					uid: imageResultId,
					userId: userId,
					model: imageModel.id,
					modelName: imageModel.name,
					tool: MediaHeadToolType.ImageEditor,
					prompt: params.prompt,
					status: MediaResultStatus.Completed,
					creditsSources: JSON.stringify(creditConsumes),
				})
				.returning();
			await tx.insert(mediaItemSchema).values({
				uid: getUUIDString(),
				userId: userId,
				mediaHeadUid: media.uid,
				visibility: false,
				mediaPaths: imagePath,
				mediaOriginUrls: resultUrl,
			});
		});

		// //更新用户token
		await updateUserCredit(userId, creditConsumes, {
			remark: `Image task id: ${imageResultId}.`,
		});

		return NextResponse.json({ message: "Success", resultUrl: `${OSS_URL_HOST}${imagePath}` });
	} catch (error: any) {
		return handleApiError(error, `${WEBNAME} - /api/v1/image/generate-image`);
	}
}
