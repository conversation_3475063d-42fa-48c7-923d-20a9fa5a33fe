import { getSessionUserId } from "@/server/auth/auth-session";
import { NextResponse } from "next/server";
import { getDB } from "@/server/db/db-client.server";
import { getUUIDString } from "@/lib/utils";
import { OSS_URL_HOST, WEBNAME } from "@/lib/constants";
import { MediaHead, mediaHeadSchema, mediaItemSchema } from "@/server/db/schema.server";
import { MediaHeadToolType, MediaHeadType, MediaResultStatus } from "@/@types/media/media-type";
import { checkUserCredit, updateUserCredit } from "@/server/utils-credits.server";
import { saveToR2 } from "@/server/r2.server";
import { handleApiError } from "@/@types/error-api";
import { genRecraft3FromFal } from "@/server/ai/recraft.server";
import { mixpanelTrackEvent } from "@/server/mixpanel.server";
import { EVENT_GEN_IMAGE_GENERATORS } from "@/lib/track-events";
import { RECRAFT_3 } from "@/config/image-models-config";

type Params = {
	prompt: string;
	size: {
		ratio: string;
		width: number;
		height: number;
	};
	style: string;
};

export async function POST(req: Request) {
	const cfIpCountryCode = req.headers.get("cf-ipcountry");
	// const cfIp = req.headers.get("cf-connecting-ip");
	const params: Params = await req.json();
	if (!params.prompt || !params.size) {
		return NextResponse.json({ status: 400, message: "Parameters is missing." });
	}

	try {
		const userId = await getSessionUserId();

		const needCredits = 12;
		const { creditConsumes, visibility } = await checkUserCredit(userId, {
			needCredits: needCredits,
		});

		if (process.env.NODE_ENV === "development") {
			console.log("params: ", params);
			console.log("creditConsumes: ", creditConsumes);
		}

		// track mixpanel event
		mixpanelTrackEvent(EVENT_GEN_IMAGE_GENERATORS, userId, {
			mp_country_code: cfIpCountryCode,
			model: RECRAFT_3.id,
			free: visibility,
			tool: "ai-vector-generator",
		});

		const imageResultId = getUUIDString();
		let resultUrls: string[] = await genRecraft3FromFal(RECRAFT_3.model, params.prompt, params.size, undefined, params.style);

		// save to r2
		const imagePaths = await Promise.all(resultUrls.map((url) => saveToR2(url)));

		// save to db
		const db = getDB();
		await db.transaction(async (tx) => {
			const [media]: MediaHead[] = await tx
				.insert(mediaHeadSchema)
				.values({
					uid: imageResultId,
					userId: userId,
					type: MediaHeadType.SVG,
					tool: MediaHeadToolType.TextToImage,
					model: RECRAFT_3.id,
					modelName: RECRAFT_3.name,
					prompt: params.prompt,
					status: MediaResultStatus.Completed,
					creditsSources: JSON.stringify(creditConsumes),
				})
				.returning();
			await tx.insert(mediaItemSchema).values(
				resultUrls.map((resultUrl, index) => ({
					uid: getUUIDString(),
					userId: userId,
					type: MediaHeadType.SVG,
					mediaHeadUid: media.uid,
					visibility: visibility,
					mediaPaths: imagePaths[index],
					mediaOriginUrls: resultUrl,
				})),
			);
		});

		// //更新用户token
		await updateUserCredit(userId, creditConsumes, {
			remark: `Image task id: ${imageResultId}.`,
		});

		return NextResponse.json({ message: "Success", resultUrls: imagePaths.map((path) => `${OSS_URL_HOST}${path}`) });
	} catch (error: any) {
		return handleApiError(error, `${WEBNAME} - /api/v1/image/generators/vector`);
	}
}
